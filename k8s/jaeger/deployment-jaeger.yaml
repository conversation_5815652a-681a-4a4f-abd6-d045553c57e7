apiVersion: apps/v1
kind: Deployment
metadata:
  name: jaeger
  namespace: jaeger
  labels:
    app: jaeger
spec:
  replicas: 1
  selector:
    matchLabels:
      app: jaeger
  template:
    metadata:
      labels:
        app: jaeger
    spec:
      nodeSelector:
        pool: np-private
      containers:
      - name: jaeger
        image: jaegertracing/all-in-one:1.22
        ports:
        - containerPort: 5775
          protocol: UDP
        - containerPort: 6831
          protocol: UDP
        - containerPort: 6832
          protocol: UDP
        - containerPort: 5778
        - containerPort: 16686
        - containerPort: 14269
        env:
        - name: COLLECTOR_ZIPKIN_HTTP_PORT
          value: "9411"
        - name: SPAN_STORAGE_TYPE
          value: "badger"
        - name: BADGER_EPHEMERAL
          value: "false"
        - name: BADGER_DIRECTORY_VALUE
          value: "/badger/data"
        - name: BADGER_DIRECTORY_KEY
          value: "/badger/key"
        # Configure 4-day retention
        - name: RETENTION_PERIOD
          value: "96h" # 4 days in hours
        resources:
          requests:
            cpu: "200m"
            memory: "512Mi"
          limits:
            cpu: "500m"
            memory: "1Gi"
        volumeMounts:
        - name: jaeger-storage
          mountPath: /badger
        livenessProbe:
          httpGet:
            path: /
            port: 14269
          initialDelaySeconds: 90
          periodSeconds: 90
          timeoutSeconds: 30
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /
            port: 14269
          initialDelaySeconds: 90
          periodSeconds: 90
          timeoutSeconds: 30
          failureThreshold: 5
      volumes:
      - name: jaeger-storage
        persistentVolumeClaim:
          claimName: jaeger-storage
