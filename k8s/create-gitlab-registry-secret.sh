#!/bin/bash

# Script para criar secret do GitLab Container Registry
# Uso: ./create-gitlab-registry-secret.sh <namespace> <username> <token>

set -e

# Função para mostrar uso
show_usage() {
    echo "Uso: $0 <namespace> <username> <token>"
    echo ""
    echo "Parâmetros:"
    echo "  namespace: Namespace do Kubernetes onde criar a secret"
    echo "  username:  <PERSON><PERSON><PERSON><PERSON> do GitLab"
    echo "  token:     Token de acesso do GitLab (glpat-...)"
    echo ""
    echo "Exemplo:"
    echo "  $0 default keviocastro **************************"
    exit 1
}

# Verificar se todos os parâmetros foram fornecidos
if [ $# -ne 3 ]; then
    echo "Erro: Número incorreto de parâmetros"
    show_usage
fi

NAMESPACE="$1"
USERNAME="$2"
TOKEN="$3"
SECRET_NAME="gitlab-registry-secret"

# Verificar se kubectl está disponível
if ! command -v kubectl &> /dev/null; then
    echo "Erro: kubectl não encontrado. Instale o kubectl primeiro."
    exit 1
fi

# Verificar se o namespace existe
if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
    echo "Aviso: Namespace '$NAMESPACE' não existe. Criando..."
    kubectl create namespace "$NAMESPACE"
fi

# Gerar auth string (base64 de username:token)
AUTH_STRING=$(echo -n "$USERNAME:$TOKEN" | base64 -w 0)

# Criar o JSON da secret
SECRET_DATA="{\"auths\":{\"registry.gitlab.com\":{\"username\":\"$USERNAME\",\"password\":\"$TOKEN\",\"auth\":\"$AUTH_STRING\"}}}"

# Codificar em base64
SECRET_DATA_B64=$(echo -n "$SECRET_DATA" | base64 -w 0)

# Criar o YAML da secret
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Secret
metadata:
  name: $SECRET_NAME
  namespace: $NAMESPACE
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: $SECRET_DATA_B64
EOF

echo "✅ Secret '$SECRET_NAME' criada com sucesso no namespace '$NAMESPACE'"
echo ""
echo "Para usar esta secret em um Pod/Deployment, adicione:"
echo "spec:"
echo "  imagePullSecrets:"
echo "  - name: $SECRET_NAME"
echo ""
echo "Ou para configurar como padrão no ServiceAccount:"
echo "kubectl patch serviceaccount default -p '{\"imagePullSecrets\": [{\"name\": \"$SECRET_NAME\"}]}' -n $NAMESPACE"
