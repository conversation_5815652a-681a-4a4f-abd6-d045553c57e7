-- Script para configurar usuário e permissões no PostgreSQL
-- Este script usa variáveis de ambiente que serão substituídas durante a execução
-- Variáveis: ${POSTGRES_APP_USER}, ${POSTGRES_APP_PASSWORD}, ${POSTGRES_APP_DATABASE}

\echo 'Iniciando configuração do usuário e database...'

-- Conectar ao database postgres para operações administrativas
\c postgres;

-- Verificar se o usuário já existe
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_user WHERE usename = '${POSTGRES_APP_USER}') THEN
        \echo 'Criando usuário ${POSTGRES_APP_USER}...'
        CREATE USER "${POSTGRES_APP_USER}" WITH PASSWORD '${POSTGRES_APP_PASSWORD}';
    ELSE
        \echo 'Usu<PERSON>rio ${POSTGRES_APP_USER} já existe. Atualizando senha...'
        ALTER USER "${POSTGRES_APP_USER}" WITH PASSWORD '${POSTGRES_APP_PASSWORD}';
    END IF;
END
$$;

-- Verificar se o database já existe
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_database WHERE datname = '${POSTGRES_APP_DATABASE}') THEN
        \echo 'Criando database ${POSTGRES_APP_DATABASE}...'
        CREATE DATABASE "${POSTGRES_APP_DATABASE}" WITH OWNER "${POSTGRES_APP_USER}";
    ELSE
        \echo 'Database ${POSTGRES_APP_DATABASE} já existe. Configurando owner...'
        ALTER DATABASE "${POSTGRES_APP_DATABASE}" OWNER TO "${POSTGRES_APP_USER}";
    END IF;
END
$$;

-- Conceder privilégios básicos ao usuário
\echo 'Configurando privilégios básicos...'
GRANT CONNECT ON DATABASE "${POSTGRES_APP_DATABASE}" TO "${POSTGRES_APP_USER}";
GRANT USAGE ON SCHEMA public TO "${POSTGRES_APP_USER}";
GRANT CREATE ON SCHEMA public TO "${POSTGRES_APP_USER}";

-- Conectar ao database da aplicação para configurações específicas
\c "${POSTGRES_APP_DATABASE}";

-- Conceder privilégios completos no schema public
\echo 'Configurando privilégios no schema public...'
GRANT ALL PRIVILEGES ON SCHEMA public TO "${POSTGRES_APP_USER}";
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO "${POSTGRES_APP_USER}";
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO "${POSTGRES_APP_USER}";
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO "${POSTGRES_APP_USER}";

-- Configurar privilégios padrão para objetos futuros
\echo 'Configurando privilégios padrão para objetos futuros...'
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO "${POSTGRES_APP_USER}";
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO "${POSTGRES_APP_USER}";
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON FUNCTIONS TO "${POSTGRES_APP_USER}";

-- Verificar configurações
\echo 'Verificando configurações...'
SELECT 
    'Database: ' || current_database() as info
UNION ALL
SELECT 
    'Current user: ' || current_user
UNION ALL
SELECT 
    'Session user: ' || session_user;

-- Listar privilégios do usuário
\echo 'Privilégios do usuário ${POSTGRES_APP_USER}:'
SELECT 
    schemaname,
    tablename,
    tableowner,
    hasinsert,
    hasselect,
    hasupdate,
    hasdelete
FROM pg_tables 
WHERE schemaname = 'public'
LIMIT 5;

-- Conectar de volta ao database postgres
\c postgres;

\echo 'Configuração concluída com sucesso!'
\echo 'Usuário: ${POSTGRES_APP_USER}'
\echo 'Database: ${POSTGRES_APP_DATABASE}'
\echo 'Schema: public com privilégios completos'
