apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: postgres-cluster-qa-monitoring
  namespace: monitoring  # namespace do seu Prometheus Operator
  labels:
    app: postgres-monitoring
    cluster: postgres-cluster-qa
spec:
  selector:
    matchLabels:
      postgres-operator.crunchydata.com/cluster: postgres-cluster-qa
      postgres-operator.crunchydata.com/role: primary
  namespaceSelector:
    matchNames:
    - postgres-qa  # namespace do seu cluster PostgreSQL
  endpoints:
  - port: exporter
    interval: 30s
    path: /metrics
    scheme: http