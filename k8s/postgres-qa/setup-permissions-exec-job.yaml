apiVersion: v1
kind: ServiceAccount
metadata:
  name: setup-permissions-sa
  namespace: postgres-qa
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: setup-permissions-role
  namespace: postgres-qa
rules:
- apiGroups: [""]
  resources: ["pods", "pods/log"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods/exec"]
  verbs: ["create", "get"]
- apiGroups: [""]
  resources: ["pods/portforward"]
  verbs: ["create"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: setup-permissions-rolebinding
  namespace: postgres-qa
subjects:
- kind: ServiceAccount
  name: setup-permissions-sa
  namespace: postgres-qa
roleRef:
  kind: Role
  name: setup-permissions-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: batch/v1
kind: Job
metadata:
  name: setup-permissions-exec-job
  namespace: postgres-qa
  labels:
    app: setup-permissions-exec
    environment: qa
spec:
  activeDeadlineSeconds: 600
  backoffLimit: 3
  template:
    metadata:
      labels:
        app: setup-permissions-exec
        job: setup-permissions-exec-job
    spec:
      restartPolicy: OnFailure
      serviceAccountName: setup-permissions-sa
      containers:
      - name: setup-permissions-exec
        image: bitnami/kubectl:1.28  # Use specific tag for stability
        imagePullPolicy: IfNotPresent
        env:
        - name: POSTGRES_CLUSTER_NAMESPACE
          value: "postgres-qa"
        - name: POSTGRES_CLUSTER_NAME
          value: "postgres-cluster-qa"
        - name: POSTGRES_ADMIN_USER
          value: "postgres"
        - name: POSTGRES_APP_USER
          value: "conversas-ai-user-qa"
        - name: POSTGRES_APP_PASSWORD
          value: "Sn6wUUlf62beriQc1ie9SkCK"
        - name: POSTGRES_APP_DATABASE
          value: "conversas_ai_qa"
        - name: POSTGRES_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-qa-secrets
              key: POSTGRES_PASSWORD
        volumeMounts:
        - name: sql-scripts
          mountPath: /sql
          readOnly: true
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        command: ["/bin/bash"]
        args:
        - -c
        - |
          set -e
          
          echo "🔧 Iniciando setup de usuário e permissões no PostgreSQL..."
          echo "Namespace: $POSTGRES_CLUSTER_NAMESPACE"
          echo "Cluster: $POSTGRES_CLUSTER_NAME"
          echo "Admin User: $POSTGRES_ADMIN_USER"
          echo "App User: $POSTGRES_APP_USER"
          echo "App Database: $POSTGRES_APP_DATABASE"
          echo ""
          
          echo "🔍 Procurando pod principal do PostgreSQL..."
          POSTGRES_POD=$(kubectl get pods -n $POSTGRES_CLUSTER_NAMESPACE \
            -l postgres-operator.crunchydata.com/cluster=$POSTGRES_CLUSTER_NAME,postgres-operator.crunchydata.com/role=master \
            -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || true)
          
          if [ -z "$POSTGRES_POD" ]; then
            echo "⚠️  Pod master não encontrado, procurando pod de instância..."
            POSTGRES_POD=$(kubectl get pods -n $POSTGRES_CLUSTER_NAMESPACE \
              -l postgres-operator.crunchydata.com/cluster=$POSTGRES_CLUSTER_NAME \
              -o jsonpath='{.items[0].metadata.name}')
          fi
          
          if [ -z "$POSTGRES_POD" ]; then
            echo "❌ Nenhum pod do PostgreSQL encontrado!"
            exit 1
          fi
          
          echo "✅ Pod encontrado: $POSTGRES_POD"
          
          echo "🔄 Verificando se o pod está pronto..."
          kubectl wait --for=condition=ready pod/$POSTGRES_POD -n $POSTGRES_CLUSTER_NAMESPACE --timeout=300s
          
          echo "📋 Copiando script SQL para o pod..."
          kubectl cp /sql/setup_user_permissions.sql $POSTGRES_CLUSTER_NAMESPACE/$POSTGRES_POD:/tmp/setup_user_permissions.sql -c database
          
          # Verificação opcional: Confirmar se o arquivo foi copiado
          kubectl exec -n $POSTGRES_CLUSTER_NAMESPACE $POSTGRES_POD -c database -- ls -l /tmp/setup_user_permissions.sql || { echo "❌ Falha ao copiar o script SQL!"; exit 1; }
          
          echo "🔄 Substituindo variáveis de ambiente no script..."
          kubectl exec -n $POSTGRES_CLUSTER_NAMESPACE $POSTGRES_POD -c database -- \
            sed -i "s/\${POSTGRES_APP_USER}/$POSTGRES_APP_USER/g" /tmp/setup_user_permissions.sql
          
          kubectl exec -n $POSTGRES_CLUSTER_NAMESPACE $POSTGRES_POD -c database -- \
            sed -i "s/\${POSTGRES_APP_PASSWORD}/$POSTGRES_APP_PASSWORD/g" /tmp/setup_user_permissions.sql
          
          kubectl exec -n $POSTGRES_CLUSTER_NAMESPACE $POSTGRES_POD -c database -- \
            sed -i "s/\${POSTGRES_APP_DATABASE}/$POSTGRES_APP_DATABASE/g" /tmp/setup_user_permissions.sql
          
          echo "🛠️  Executando script de setup no pod PostgreSQL..."
          kubectl exec -n $POSTGRES_CLUSTER_NAMESPACE $POSTGRES_POD -c database -- \
            env PGPASSWORD="$POSTGRES_ADMIN_PASSWORD" \
            psql -U $POSTGRES_ADMIN_USER -d postgres -f /tmp/setup_user_permissions.sql
          
          if [ $? -eq 0 ]; then
            echo ""
            echo "✅ Script de setup executado com sucesso!"
            
            echo ""
            echo "🧪 Testando conexão com usuário $POSTGRES_APP_USER..."
            
            if kubectl exec -n $POSTGRES_CLUSTER_NAMESPACE $POSTGRES_POD -c database -- \
              env PGPASSWORD="$POSTGRES_APP_PASSWORD" \
              psql -U $POSTGRES_APP_USER -d $POSTGRES_APP_DATABASE -c "SELECT current_user, current_database(), version();" > /dev/null 2>&1; then
              
              echo "✅ Teste de conexão bem-sucedido!"
              
              echo ""
              echo "🧪 Testando permissões de criação de tabela..."
              kubectl exec -n $POSTGRES_CLUSTER_NAMESPACE $POSTGRES_POD -c database -- \
                env PGPASSWORD="$POSTGRES_APP_PASSWORD" \
                psql -U $POSTGRES_APP_USER -d $POSTGRES_APP_DATABASE -c "
                  DROP TABLE IF EXISTS permission_test_exec;
                  CREATE TABLE permission_test_exec (id SERIAL PRIMARY KEY, test_column VARCHAR(50));
                  INSERT INTO permission_test_exec (test_column) VALUES ('Teste de permissão via kubectl exec');
                  SELECT * FROM permission_test_exec;
                  DROP TABLE permission_test_exec;
                "
              
              if [ $? -eq 0 ]; then
                echo "✅ Teste de permissões bem-sucedido!"
              else
                echo "⚠️  Aviso: Falha no teste de permissões, mas usuário foi configurado"
              fi
            else
              echo "⚠️  Aviso: Falha no teste de conexão, mas setup foi executado"
            fi
          else
            echo "❌ Erro ao executar o script de setup"
            exit 1
          fi
          
          kubectl exec -n $POSTGRES_CLUSTER_NAMESPACE $POSTGRES_POD -c database -- \
            rm -f /tmp/setup_user_permissions.sql
          
          echo ""
          echo "🎉 Job setup-permissions-exec-job concluído com sucesso!"
          echo "Usuario: $POSTGRES_APP_USER configurado no banco: $POSTGRES_APP_DATABASE"
      volumes:
      - name: sql-scripts
        configMap:
          name: sql-permissions-config
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: sql-permissions-config
  namespace: postgres-qa
  labels:
    app: sql-permissions
data:
  setup_user_permissions.sql: |
    -- Script para criar usuário (se não existir) e definir todas as permissões
    -- Este script deve ser executado como superuser (postgres)
    -- Utiliza variáveis de ambiente: ${POSTGRES_APP_USER}, ${POSTGRES_APP_PASSWORD}, ${POSTGRES_APP_DATABASE}

    \echo 'Iniciando setup de permissões para usuário: ${POSTGRES_APP_USER}'

    -- Verificar e criar usuário se necessário
    DO $$
    BEGIN
        -- Verificar se o usuário existe, se não, criar
        IF NOT EXISTS (
            SELECT FROM pg_catalog.pg_roles 
            WHERE rolname = '${POSTGRES_APP_USER}'
        ) THEN
            EXECUTE format('CREATE USER %I WITH CREATEDB CREATEROLE LOGIN PASSWORD %L', 
                          '${POSTGRES_APP_USER}', 
                          '${POSTGRES_APP_PASSWORD}');
            RAISE NOTICE 'Usuário % criado com sucesso', '${POSTGRES_APP_USER}';
        ELSE
            RAISE NOTICE 'Usuário % já existe', '${POSTGRES_APP_USER}';
        END IF;
    END
    $$;

    -- Conceder permissões no banco de dados
    SELECT format('GRANT ALL PRIVILEGES ON DATABASE %I TO %I', 
                   '${POSTGRES_APP_DATABASE}', 
                   '${POSTGRES_APP_USER}') as grant_db_command \gexec

    -- Conectar ao banco da aplicação para definir permissões no schema
    \c ${POSTGRES_APP_DATABASE};

    -- Conceder todas as permissões no schema public
    SELECT format('GRANT ALL PRIVILEGES ON SCHEMA public TO %I', '${POSTGRES_APP_USER}') as grant_schema_command \gexec
    SELECT format('GRANT CREATE ON SCHEMA public TO %I', '${POSTGRES_APP_USER}') as grant_create_command \gexec
    SELECT format('GRANT USAGE ON SCHEMA public TO %I', '${POSTGRES_APP_USER}') as grant_usage_command \gexec

    -- Conceder permissões em todas as tabelas existentes
    SELECT format('GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO %I', '${POSTGRES_APP_USER}') as grant_tables_command \gexec

    -- Conceder permissões em todas as sequências existentes
    SELECT format('GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO %I', '${POSTGRES_APP_USER}') as grant_sequences_command \gexec

    -- Conceder permissões em todas as funções existentes
    SELECT format('GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO %I', '${POSTGRES_APP_USER}') as grant_functions_command \gexec

    -- Definir permissões padrão para objetos futuros
    SELECT format('ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO %I', '${POSTGRES_APP_USER}') as alter_default_tables \gexec
    SELECT format('ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO %I', '${POSTGRES_APP_USER}') as alter_default_sequences \gexec
    SELECT format('ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO %I', '${POSTGRES_APP_USER}') as alter_default_functions \gexec

    -- Tornar o usuário proprietário do schema public (resolve problemas de permissão)
    SELECT format('ALTER SCHEMA public OWNER TO %I', '${POSTGRES_APP_USER}') as alter_schema_owner \gexec

    -- Verificar permissões
    \echo 'Verificando permissões concedidas:'
    SELECT 
        'Database Privileges' as type,
        datname as name,
        array_agg(privilege_type) as privileges
    FROM information_schema.database_privileges 
    WHERE grantee = '${POSTGRES_APP_USER}'
    GROUP BY datname;

    SELECT 
        'Schema Privileges' as type,
        schema_name as name,
        array_agg(privilege_type) as privileges
    FROM information_schema.schema_privileges 
    WHERE grantee = '${POSTGRES_APP_USER}'
    GROUP BY schema_name;

    -- Mostrar informações do usuário
    \echo 'Informações do usuário criado/configurado:'
    SELECT 
        rolname,
        rolsuper,
        rolinherit,
        rolcreaterole,
        rolcreatedb,
        rolcanlogin,
        rolconnlimit
    FROM pg_roles 
    WHERE rolname = '${POSTGRES_APP_USER}';

    \echo 'Setup de usuário e permissões concluído com sucesso para: ${POSTGRES_APP_USER}'