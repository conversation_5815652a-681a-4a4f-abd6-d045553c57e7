-- Table: contexto_turmas
CREATE TABLE contexto_turmas (
    turmas TEXT,
    data_ultima_atualizacao TIMESTAMP,
    id_empresa TEXT
);
COMMENT ON TABLE contexto_turmas IS 'Tabela para armazenar informações de turmas';
COMMENT ON COLUMN contexto_turmas.id_empresa IS 'Identificador da empresa';

-- Table: contexto_fases
CREATE TABLE contexto_fases (
    codigo_fase BIGINT,
    nome_fase TEXT,
    descricao_fase TEXT,
    instrucao_ia_fase TEXT,
    data_ultima_atualizacao TIMESTAMP,
    id_empresa TEXT
);
COMMENT ON TABLE contexto_fases IS 'Tabela para armazenar fases do sistema';

-- Table: gymbot_tokens
CREATE TABLE gymbot_tokens (
    id_empresa TEXT,
    token TEXT,
    data_ultima_atualizacao TIMESTAMP
);
COMMENT ON TABLE gymbot_tokens IS 'Tabela para armazenar tokens do Gymbot';

-- Table: empresas
CREATE TABLE empresas (
    id_empresa TEXT,
    telefone_responsavel_empresa TEXT,
    email_responsavel_empresa TEXT,
    contexto_empresa_json JSONB,
    data_ultima_atualizacao TIMESTAMP,
    integracao TEXT
);
COMMENT ON TABLE empresas IS 'Tabela para armazenar informações das empresas';

-- Table: configuration
CREATE TABLE configuration (
    id_empresa TEXT,
    config TEXT,
    data_ultima_atualizacao TIMESTAMP
);
COMMENT ON TABLE configuration IS 'Tabela para armazenar configurações';
COMMENT ON COLUMN configuration.id_empresa IS 'Identificador da empresa';
COMMENT ON COLUMN configuration.config IS 'Configuração definida';
COMMENT ON COLUMN configuration.data_ultima_atualizacao IS 'Última atualização dos dados';

-- Table: stickers
CREATE TABLE stickers (
    sticker TEXT NOT NULL,
    data_ultima_atualizacao TIMESTAMP NOT NULL,
    id_empresa TEXT NOT NULL
);
COMMENT ON TABLE stickers IS 'Tabela para armazenar stickers criados no sistema CRM IA';
COMMENT ON COLUMN stickers.sticker IS 'Identificador único do sticker';
COMMENT ON COLUMN stickers.data_ultima_atualizacao IS 'Data e hora da última atualização do sticker';
COMMENT ON COLUMN stickers.id_empresa IS 'Identificador da empresa associada ao sticker';

-- Table: redes
CREATE TABLE redes (
    redes_json JSONB,
    data_ultima_atualizacao TIMESTAMP,
    id_empresa TEXT
);
COMMENT ON TABLE redes IS 'Tabela para armazenar informações de redes em formato JSON';

-- Table: contexto_planos
CREATE TABLE contexto_planos (
    planos_json TEXT,
    data_ultima_atualizacao TIMESTAMP,
    id_empresa TEXT
);
COMMENT ON TABLE contexto_planos IS 'Tabela para armazenar planos em formato JSON';

-- Table: vendas_conversas
CREATE TABLE vendas_conversas (
    aulaavulsadiaria BIGINT,
    cod_empresafinanceiro BIGINT,
    contrato_codigo BIGINT,
    duracao_contrato BIGINT,
    modalidadediaria TEXT,
    movimentacaodescricao TEXT,
    movproduto_datalancamento TIMESTAMP,
    nomecomprador TEXT,
    nomeempresa TEXT,
    nomeproduto TEXT,
    nomeresponsavel TEXT,
    origemsistema BIGINT,
    personal BIGINT,
    pessoa_codigo BIGINT,
    pessoa_nome TEXT,
    plano_codigo BIGINT,
    qtdproduto BIGINT,
    situacaocontrato TEXT,
    situacaomovproduto TEXT,
    tipocomprador TEXT,
    tipoproduto TEXT,
    total DOUBLE PRECISION,
    usuario TEXT,
    vendaavulsa BIGINT,
    _chave TEXT
);
COMMENT ON TABLE vendas_conversas IS 'Tabela para armazenar informações de vendas e conversas';

-- Table: metas_diarias
CREATE TABLE metas_diarias (
    phone TEXT,
    classification TEXT,
    action_description TEXT,
    id_empresa TEXT,
    id_conversa TEXT,
    data_analise TIMESTAMP
);
COMMENT ON TABLE metas_diarias IS 'Tabela para armazenar os resultados das metas diárias das interações';
COMMENT ON COLUMN metas_diarias.phone IS 'Número de telefone do usuário';
COMMENT ON COLUMN metas_diarias.classification IS 'Classificação atribuída à conversa';
COMMENT ON COLUMN metas_diarias.action_description IS 'Descrição da ação realizada';
COMMENT ON COLUMN metas_diarias.id_empresa IS 'Identificador da empresa';
COMMENT ON COLUMN metas_diarias.id_conversa IS 'Identificador único da conversa';
COMMENT ON COLUMN metas_diarias.data_analise IS 'Data e hora da análise da meta diária';

-- Table: memories
CREATE TABLE memories (
    phone TEXT,
    memory TEXT,
    id_empresa TEXT,
    data_ultima_atualizacao TIMESTAMP
);
COMMENT ON TABLE memories IS 'Tabela para armazenar memórias dos usuários';
COMMENT ON COLUMN memories.phone IS 'Telefone do usuário';
COMMENT ON COLUMN memories.memory IS 'Memórias do usuário';
COMMENT ON COLUMN memories.id_empresa IS 'ID da empresa';
COMMENT ON COLUMN memories.data_ultima_atualizacao IS 'Data da última atualização';

-- Table: logs_requests_zapi
CREATE TABLE logs_requests_zapi (
    function TEXT,
    request TEXT,
    response TEXT,
    status_code TEXT,
    id_empresa TEXT,
    data_requisicao TIMESTAMP
);
COMMENT ON TABLE logs_requests_zapi IS 'Tabela para armazenar logs de requisições ZAPI';
COMMENT ON COLUMN logs_requests_zapi.function IS 'Função no Python relacionada ao request';
COMMENT ON COLUMN logs_requests_zapi.request IS 'Comando cURL do request';
COMMENT ON COLUMN logs_requests_zapi.response IS 'Resposta recebida da requisição';
COMMENT ON COLUMN logs_requests_zapi.status_code IS 'Código de status da resposta';
COMMENT ON COLUMN logs_requests_zapi.id_empresa IS 'Identificação da empresa';
COMMENT ON COLUMN logs_requests_zapi.data_requisicao IS 'Data e hora em que a requisição foi feita';

-- Table: conversas
CREATE TABLE conversas (
    enviado_por TEXT,
    id_usuario TEXT,
    data_envio TIMESTAMP,
    mensagem TEXT,
    status TEXT,
    telefone TEXT,
    id_empresa TEXT,
    prompt_tokens BIGINT,
    completion_tokens BIGINT,
    tipo_mensagem TEXT,
    n_chars BIGINT,
    n_seconds DOUBLE PRECISION,
    model TEXT,
    id_conversa TEXT,
    id_mensagem TEXT,
    provedor_mensagem TEXT,
    id_contexto TEXT,
    situacao TEXT,
    departamento TEXT,
    colaborador TEXT
);
COMMENT ON TABLE conversas IS 'Tabela para armazenar mensagens de conversas';
COMMENT ON COLUMN conversas.n_chars IS 'Quantidade de caracteres na mensagem (tts)';
COMMENT ON COLUMN conversas.n_seconds IS 'Quantidade de segundos do áudio (stt)';
COMMENT ON COLUMN conversas.model IS 'O modelo que gerou a mensagem';
COMMENT ON COLUMN conversas.id_mensagem IS 'ID da mensagem específica';
COMMENT ON COLUMN conversas.provedor_mensagem IS 'Plataforma de comunicação';

-- Table: gymbot_departamentos
CREATE TABLE gymbot_departamentos (
    id_empresa TEXT,
    departamento TEXT,
    descricao TEXT,
    data_ultima_atualizacao TIMESTAMP,
    id_departamento TEXT
);
COMMENT ON TABLE gymbot_departamentos IS 'Tabela para armazenar departamentos do Gymbot';

-- Table: datas_execucoes
CREATE TABLE datas_execucoes (
    tabela TEXT,
    data_ultima_execucao TIMESTAMP,
    data_ultima_execucao_up TEXT
);
COMMENT ON TABLE datas_execucoes IS 'Tabela para armazenar datas de execução de processos';

-- Table: logs_erros_terminais
CREATE TABLE logs_erros_terminais (
    traceback TEXT,
    data_registro TIMESTAMP,
    ambiente TEXT
);
COMMENT ON TABLE logs_erros_terminais IS 'Tabela para armazenar logs de erros em terminais';

-- Table: logs_mensagens_enviadas
CREATE TABLE logs_mensagens_enviadas (
    status TEXT,
    phone TEXT,
    response TEXT,
    id_empresa TEXT,
    data_envio TIMESTAMP,
    traceback TEXT
);
COMMENT ON TABLE logs_mensagens_enviadas IS 'Tabela para armazenar logs de mensagens enviadas';
COMMENT ON COLUMN logs_mensagens_enviadas.traceback IS 'Traceback do erro';

-- Table: contexto_usuario
CREATE TABLE contexto_usuario (
    id_usuario_unico TEXT,
    id_usuario TEXT,
    contexto_usuario_json TEXT,
    data_ultima_atualizacao TIMESTAMP,
    telefone TEXT,
    id_empresa TEXT,
    fase_atual TEXT,
    id_matriz TEXT,
    origin TEXT,
    origin_last_update TEXT
);
COMMENT ON TABLE contexto_usuario IS 'Tabela para armazenar contexto dos usuários';

-- Table: status_envios
CREATE TABLE status_envios (
    id_mensagem TEXT,
    status_mensagem VARCHAR(20),
    provedor_mensagem TEXT,
    id_empresa TEXT,
    telefone TEXT,
    data_registro TIMESTAMP
);
COMMENT ON TABLE status_envios IS 'Tabela para armazenar status de envio de mensagens';
COMMENT ON COLUMN status_envios.id_mensagem IS 'ID da mensagem';
COMMENT ON COLUMN status_envios.status_mensagem IS 'Status da mensagem em relação a recebimento/leitura';
COMMENT ON COLUMN status_envios.provedor_mensagem IS 'Provedor do serviço de mensageria';
COMMENT ON COLUMN status_envios.id_empresa IS 'ID da empresa relacionada';
COMMENT ON COLUMN status_envios.telefone IS 'Telefone do cliente relacionado';
COMMENT ON COLUMN status_envios.data_registro IS 'Data do registro de status';

-- Table: logs_conexao_whatsapp
CREATE TABLE logs_conexao_whatsapp (
    error TEXT,
    timestamp TIMESTAMP,
    chave_empresa TEXT
);
COMMENT ON TABLE logs_conexao_whatsapp IS 'Tabela para armazenar logs de conexão com WhatsApp';

-- Table: logs_requests_gymbot
CREATE TABLE logs_requests_gymbot (
    function TEXT,
    request TEXT,
    response TEXT,
    status_code TEXT,
    id_empresa TEXT,
    data_requisicao TIMESTAMP
);
COMMENT ON TABLE logs_requests_gymbot IS 'Tabela para armazenar logs de requisições do Gymbot';
COMMENT ON COLUMN logs_requests_gymbot.function IS 'Função Python que originou a requisição';
COMMENT ON COLUMN logs_requests_gymbot.request IS 'Comando cURL enviado';
COMMENT ON COLUMN logs_requests_gymbot.response IS 'Resposta recebida da API';
COMMENT ON COLUMN logs_requests_gymbot.status_code IS 'Código de status HTTP retornado';
COMMENT ON COLUMN logs_requests_gymbot.id_empresa IS 'ID da empresa que fez a requisição';
COMMENT ON COLUMN logs_requests_gymbot.data_requisicao IS 'Timestamp de quando a requisição foi efetuada';

-- Table: sessions
CREATE TABLE sessions (
    token TEXT,
    api_key TEXT,
    creation TIMESTAMP,
    expiration BIGINT,
    id_empresa TEXT
);
COMMENT ON TABLE sessions IS 'Tabela para armazenar sessões de autenticação';
COMMENT ON COLUMN sessions.token IS 'Token utilizado para autentificar a chave de api';
COMMENT ON COLUMN sessions.api_key IS 'A chave de api';
COMMENT ON COLUMN sessions.creation IS 'Data de criação';
COMMENT ON COLUMN sessions.expiration IS 'Tempo de expiração do token';
COMMENT ON COLUMN sessions.id_empresa IS 'ID da empresa';

-- Table: contexto_personalidade
CREATE TABLE contexto_personalidade (
    id_empresa TEXT,
    personalidade TEXT,
    data_ultima_atualizacao TIMESTAMP
);
COMMENT ON TABLE contexto_personalidade IS 'Tabela para armazenar informações de personalidade';

-- Table: api_keys
CREATE TABLE api_keys (
    api_key TEXT,
    "user" TEXT,
    last_data TIMESTAMP,
    active BOOLEAN,
    creation_data TIMESTAMP,
    environment_key TEXT,
    id_empresa TEXT
);
COMMENT ON TABLE api_keys IS 'Tabela para armazenar chaves de API';
COMMENT ON COLUMN api_keys.api_key IS 'Chave de api';
COMMENT ON COLUMN api_keys.user IS 'Usuário';
COMMENT ON COLUMN api_keys.last_data IS 'Última vez que api foi autenticada';
COMMENT ON COLUMN api_keys.creation_data IS 'TIMESTAMP(CURRENT_TIMESTAMP, "America/Sao_Paulo")';
COMMENT ON COLUMN api_keys.environment_key IS 'Chave master usada para fazer a autentificação';
COMMENT ON COLUMN api_keys.id_empresa IS 'ID da empresa';

-- Table: contexto_produtos
CREATE TABLE contexto_produtos (
    produtos TEXT,
    data_ultima_atualizacao TIMESTAMP,
    id_empresa TEXT
);
COMMENT ON TABLE contexto_produtos IS 'Tabela para armazenar informações de produtos';

-- Table: logs_requests_sistema_pacto
CREATE TABLE logs_requests_sistema_pacto (
    function TEXT,
    request TEXT,
    response TEXT,
    status_code TEXT,
    id_empresa TEXT,
    data_requisicao TIMESTAMP
);
COMMENT ON TABLE logs_requests_sistema_pacto IS 'Tabela para armazenar logs de requisições do sistema Pacto';
COMMENT ON COLUMN logs_requests_sistema_pacto.function IS 'Nome da função Python que originou a requisição';
COMMENT ON COLUMN logs_requests_sistema_pacto.request IS 'Comando cURL enviado para a API';
COMMENT ON COLUMN logs_requests_sistema_pacto.response IS 'Corpo da resposta retornada pela API';
COMMENT ON COLUMN logs_requests_sistema_pacto.status_code IS 'Código de status HTTP da resposta';
COMMENT ON COLUMN logs_requests_sistema_pacto.id_empresa IS 'Identificador da empresa que fez a requisição';
COMMENT ON COLUMN logs_requests_sistema_pacto.data_requisicao IS 'Timestamp de quando a requisição foi efetuada';

-- Table: pacto_users
CREATE TABLE pacto_users (
    id_empresa TEXT,
    login TEXT,
    senha TEXT
);
COMMENT ON TABLE pacto_users IS 'Tabela para armazenar usuários do sistema Pacto';

-- Table: conversas_analise
CREATE TABLE conversas_analise (
    phone TEXT,
    analysis TEXT,
    success BOOLEAN,
    id_empresa TEXT,
    id_conversa TEXT,
    data_analise TIMESTAMP,
    prompt JSONB
);
COMMENT ON TABLE conversas_analise IS 'Tabela que armazena informações sobre análises realizadas em conversas de clientes';
COMMENT ON COLUMN conversas_analise.phone IS 'Número de telefone associado à análise';
COMMENT ON COLUMN conversas_analise.analysis IS 'Texto ou resultado da análise realizada';
COMMENT ON COLUMN conversas_analise.success IS 'Indica se a análise foi bem-sucedida';
COMMENT ON COLUMN conversas_analise.id_empresa IS 'Identificador único da empresa';
COMMENT ON COLUMN conversas_analise.id_conversa IS 'Identificador único da conversa associada';
COMMENT ON COLUMN conversas_analise.data_analise IS 'Data que aconteceu a análise';
COMMENT ON COLUMN conversas_analise.prompt IS 'Prompt que foi passado para análise';

-- Table: notificacoes_schema
CREATE TABLE notificacoes_schema (
    id_empresa TEXT,
    schema_notificacao JSONB,
    descricao_original TEXT,
    data_envio TIMESTAMP,
    notification_type TEXT,
    category TEXT
);
COMMENT ON TABLE notificacoes_schema IS 'Tabela para armazenar esquemas de notificações';

-- Table: instances
CREATE TABLE instances (
    id_empresa TEXT,
    instance_id TEXT,
    token TEXT,
    is_rede BOOLEAN
);
COMMENT ON TABLE instances IS 'Tabela para armazenar instâncias';

-- Table: strikes_usuarios
CREATE TABLE strikes_usuarios (
    telefone TEXT,
    id_empresa TEXT,
    data_registro TEXT
);
COMMENT ON TABLE strikes_usuarios IS 'Tabela para armazenar strikes de usuários';

-- Table: logs_requests_conversas_ai
CREATE TABLE logs_requests_conversas_ai (
    origin TEXT,
    remote_addr TEXT,
    method TEXT,
    path TEXT,
    url TEXT,
    headers JSONB,
    body JSONB,
    args JSONB,
    date TIMESTAMP,
    curl_command TEXT,
    ip TEXT,
    api_key TEXT,
    id TEXT,
    response TEXT,
    status_code BIGINT,
    response_headers TEXT
);
COMMENT ON TABLE logs_requests_conversas_ai IS 'Tabela para armazenar logs de requisições da API Conversas AI';
COMMENT ON COLUMN logs_requests_conversas_ai.origin IS 'Origem da requisição (domínio ou IP de origem)';
COMMENT ON COLUMN logs_requests_conversas_ai.remote_addr IS 'Endereço IP remoto do cliente que fez a requisição';
COMMENT ON COLUMN logs_requests_conversas_ai.method IS 'Método HTTP utilizado na requisição (GET, POST, etc.)';
COMMENT ON COLUMN logs_requests_conversas_ai.path IS 'Caminho do endpoint acessado';
COMMENT ON COLUMN logs_requests_conversas_ai.url IS 'URL completa da requisição, incluindo query string';
COMMENT ON COLUMN logs_requests_conversas_ai.headers IS 'Cabeçalhos HTTP enviados na requisição';
COMMENT ON COLUMN logs_requests_conversas_ai.body IS 'Corpo da requisição em formato JSON';
COMMENT ON COLUMN logs_requests_conversas_ai.args IS 'Parâmetros de query string, armazenados como JSON';
COMMENT ON COLUMN logs_requests_conversas_ai.date IS 'Data e hora da requisição em UTC';
COMMENT ON COLUMN logs_requests_conversas_ai.curl_command IS 'Comando cURL para replicar a requisição';
COMMENT ON COLUMN logs_requests_conversas_ai.ip IS 'Endereço IP de quem fez a requisição';
COMMENT ON COLUMN logs_requests_conversas_ai.api_key IS 'Chave de autenticação utilizada';
COMMENT ON COLUMN logs_requests_conversas_ai.id IS 'ID único da requisição';
COMMENT ON COLUMN logs_requests_conversas_ai.response IS 'Resposta da requisição em formato JSON';
COMMENT ON COLUMN logs_requests_conversas_ai.status_code IS 'Código de status HTTP da resposta';
COMMENT ON COLUMN logs_requests_conversas_ai.response_headers IS 'Cabeçalhos HTTP da resposta';

-- Table: contexto_campanhas
CREATE TABLE contexto_campanhas (
    id_empresa TEXT NOT NULL,
    nome TEXT NOT NULL,
    instrucao TEXT NOT NULL,
    keyword TEXT NOT NULL,
    data_inicio TIMESTAMP,
    data_fim TIMESTAMP,
    imagem TEXT,
    is_template BOOLEAN,
    data_atualizacao TIMESTAMP,
    id_campanha TEXT,
    whatsapp_link TEXT
);
COMMENT ON TABLE contexto_campanhas IS 'Tabela para armazenar informações de campanhas';

-- Table: contexto_academia
CREATE TABLE contexto_academia (
    contexto_academia_json TEXT,
    data_ultima_atualizacao TIMESTAMP,
    id_empresa TEXT,
    telefone TEXT,
    model_source TEXT,
    telefone_responsavel_empresa TEXT,
    email_responsavel_empresa TEXT,
    messager_channel TEXT
);
COMMENT ON TABLE contexto_academia IS 'Tabela para armazenar contexto da academia';
COMMENT ON COLUMN contexto_academia.telefone_responsavel_empresa IS 'Telefone do responsável pelo Conversas.AI';
COMMENT ON COLUMN contexto_academia.email_responsavel_empresa IS 'Email do responsável pelo Conversas.AI';
COMMENT ON COLUMN contexto_academia.messager_channel IS 'Canal de mensagens (z_api ou gym_bot)';
