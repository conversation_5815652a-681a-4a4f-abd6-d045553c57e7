apiVersion: batch/v1
kind: Job
metadata:
  name: create-tables-job
  namespace: postgres-qa
  labels:
    app: create-tables
spec:
  # Job deve completar em no máximo 10 minutos
  activeDeadlineSeconds: 600
  # Tentar até 2 vezes se falhar
  backoffLimit: 2
  template:
    metadata:
      labels:
        app: create-tables
    spec:
      restartPolicy: OnFailure
      containers:
      - name: create-tables
        image: postgres:15-alpine
        imagePullPolicy: IfNotPresent
        env:
        # Variáveis de ambiente para conexão com PostgreSQL
        - name: PGHOST
          value: "postgres-cluster-qa-primary.postgres-qa.svc.cluster.local"
        - name: PGPORT
          value: "5432"
        - name: PGUSER
          valueFrom:
            secretKeyRef:
              name: postgres-cluster-qa-pguser-conversas-ai-user-qa
              key: user
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-cluster-qa-pguser-conversas-ai-user-qa
              key: password
        - name: PGDATABASE
          valueFrom:
            secretKeyRef:
              name: postgres-cluster-qa-pguser-conversas-ai-user-qa
              key: dbname
        volumeMounts:
        - name: sql-scripts
          mountPath: /sql
          readOnly: true
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        # Comandos para executar o script SQL
        command: ["/bin/sh"]
        args: 
        - -c
        - |
          echo "🗃️  Iniciando criação das tabelas no PostgreSQL QA..."
          echo "Host: $PGHOST"
          echo "Database: $PGDATABASE"
          echo "User: $PGUSER"
          echo "Port: $PGPORT"
          echo ""
          echo "📋 Executando script create_tables.sql..."
          
          # Verificar conexão
          if ! pg_isready -h $PGHOST -p $PGPORT -U $PGUSER -d $PGDATABASE; then
            echo "❌ Erro: Não foi possível conectar ao PostgreSQL"
            exit 1
          fi
          
          echo "✅ Conexão com PostgreSQL estabelecida"
          
          # Executar o script SQL
          if psql -h $PGHOST -p $PGPORT -U $PGUSER -d $PGDATABASE -f /sql/create_tables.sql; then
            echo "✅ Script create_tables.sql executado com sucesso!"
            
            # Listar as tabelas criadas
            echo ""
            echo "📋 Tabelas criadas no banco:"
            psql -h $PGHOST -p $PGPORT -U $PGUSER -d $PGDATABASE -c "\dt"
            
          else
            echo "❌ Erro ao executar o script create_tables.sql"
            exit 1
          fi
          
          echo ""
          echo "🎉 Job create-tables-job concluído com sucesso!"
      volumes:
      - name: sql-scripts
        configMap:
          name: sql-scripts-config
