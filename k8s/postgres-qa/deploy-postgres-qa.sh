#!/bin/bash

set -e

echo "🚀 Deploying PostgreSQL QA environment using Crunchy Data Postgres Operator..."

# Verificar se kubectl está disponível
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl não encontrado. Por favor, instale kubectl primeiro."
    exit 1
fi

# Step 1: Instalar PGO (Postgres Operator) seguindo documentação oficial
echo "📦 Installing PGO, the Postgres Operator..."
echo "   Creating postgres-operator namespace..."
kubectl apply -k https://github.com/CrunchyData/postgres-operator-examples/kustomize/install/namespace

echo "   Installing PGO operator..."
kubectl apply --server-side -k https://github.com/CrunchyData/postgres-operator-examples/kustomize/install/default

# Aguardar o operator ficar pronto
echo "   Waiting for operator to be ready..."
echo "   This may take a few minutes..."
kubectl -n postgres-operator wait --for=condition=Ready pod --selector=postgres-operator.crunchydata.com/control-plane=postgres-operator --timeout=300s

echo "   ✅ PGO Operator is running!"

# Verificar status do operator
echo "📋 Checking PGO status:"
kubectl -n postgres-operator get pods --selector=postgres-operator.crunchydata.com/control-plane=postgres-operator --field-selector=status.phase=Running

# Step 2: Criar namespace para QA
echo "📁 Creating QA namespace..."
kubectl apply -f namespace.yaml

# Step 3: Aplicar cluster PostgreSQL
echo "🐘 Creating PostgreSQL cluster for QA..."
kubectl apply -f postgres-cluster-qa.yaml

# Aguardar o cluster ficar pronto
echo "   Waiting for PostgreSQL cluster to be ready..."
echo "   This may take a few minutes while images are pulled and cluster is initialized..."
kubectl -n postgres-qa wait --for=condition=PGBackRestReplicaRepoReady postgrescluster/postgres-cluster-qa --timeout=600s

echo "✅ PostgreSQL QA deployment completed!"

# Step 4: Setup de permissões automático
echo "🔧 Setting up database permissions..."
echo "   Applying SQL permissions ConfigMap..."
if kubectl apply -f sql-permissions-configmap.yaml 2>/dev/null; then
    echo "   ConfigMap applied successfully!"
else
    echo "   ⚠️  ConfigMap not found, skipping permissions setup"
fi

echo "   Applying permissions setup job..."
if kubectl apply -f setup-permissions-exec-job.yaml 2>/dev/null; then
    echo "   Waiting for permissions setup to complete..."
    kubectl wait --for=condition=complete job/setup-permissions-exec-job -n postgres-qa --timeout=300s || {
        echo "   ⚠️  Permissions job didn't complete in time, check logs:"
        echo "   kubectl logs job/setup-permissions-exec-job -n postgres-qa"
    }
    echo "   ✅ Permissions setup completed!"
else
    echo "   ⚠️  Permissions job file not found, you can run it manually later"
fi

echo ""
echo "📋 Checking deployment status..."
kubectl get all -n postgres-qa

echo ""
echo "🔍 PostgreSQL Cluster Status:"
kubectl -n postgres-qa get postgrescluster

echo ""
echo "📝 Detailed cluster information:"
kubectl -n postgres-qa describe postgrescluster postgres-cluster-qa

echo ""
echo "🔌 Connection Information:"
echo "========================================"
echo "Cluster Name: postgres-cluster-qa"
echo "Namespace: postgres-qa"
echo "Database: conversas_ai_qa"
echo "Username: conversas-ai-user-qa"
echo ""
echo "🔑 To get connection details:"
echo "   # Get all connection info:"
echo '   kubectl -n postgres-qa get secret postgres-cluster-qa-pguser-conversas-ai-user-qa -o go-template="{{.data.uri | base64decode}}"'
echo ""
echo "   # Get just the password:"
echo '   kubectl -n postgres-qa get secret postgres-cluster-qa-pguser-conversas-ai-user-qa -o go-template="{{.data.password | base64decode}}"'
echo ""
echo "🔌 Connect via psql (direct connection):"
echo '   psql $(kubectl -n postgres-qa get secrets postgres-cluster-qa-pguser-conversas-ai-user-qa -o go-template="{{.data.uri | base64decode}}")'
echo ""
echo "🔌 Connect via port-forward:"
echo "   # In one terminal:"
echo '   PG_CLUSTER_PRIMARY_POD=$(kubectl get pod -n postgres-qa -o name -l postgres-operator.crunchydata.com/cluster=postgres-cluster-qa,postgres-operator.crunchydata.com/role=master)'
echo '   kubectl -n postgres-qa port-forward "${PG_CLUSTER_PRIMARY_POD}" 5432:5432'
echo ""
echo "   # In another terminal:"
echo '   PG_CLUSTER_USER_SECRET_NAME=postgres-cluster-qa-pguser-conversas-ai-user-qa'
echo '   PGPASSWORD=$(kubectl get secrets -n postgres-qa "${PG_CLUSTER_USER_SECRET_NAME}" -o go-template="{{.data.password | base64decode}}") \'
echo '   PGUSER=$(kubectl get secrets -n postgres-qa "${PG_CLUSTER_USER_SECRET_NAME}" -o go-template="{{.data.user | base64decode}}") \'
echo '   PGDATABASE=$(kubectl get secrets -n postgres-qa "${PG_CLUSTER_USER_SECRET_NAME}" -o go-template="{{.data.dbname | base64decode}}") \'
echo '   psql -h localhost'
echo ""
echo "💡 Remember: After connecting, you may want to create a user schema:"
echo "   CREATE SCHEMA conversas-ai-user-qa AUTHORIZATION \"conversas-ai-user-qa\";"
echo ""
echo "🔧 Useful commands:"
echo "   # View cluster status:"
echo "   kubectl -n postgres-qa get postgrescluster"
echo ""
echo "   # View all resources:"
echo "   kubectl -n postgres-qa get all"
echo ""
echo "   # View cluster events:"
echo "   kubectl -n postgres-qa get events --sort-by='.firstTimestamp'"
echo ""
echo "   # Delete cluster (if needed):"
echo "   kubectl -n postgres-qa delete postgrescluster postgres-cluster-qa"
