apiVersion: batch/v1
kind: Job
metadata:
  name: pg-migrate-job
  namespace: postgres-qa
  labels:
    app: pg-migrate
spec:
  # Job deve completar em no máximo 6 horas
  activeDeadlineSeconds: 21600
  # Tentar até 3 vezes se falhar
  backoffLimit: 3
  template:
    metadata:
      labels:
        app: pg-migrate
    spec:
      restartPolicy: OnFailure
      imagePullSecrets:
        - name: gitlab-registry-secret
      containers:
      - name: pg-migrate
        image: registry.gitlab.com/plataformazw/ia/orion/pg-migrate:feature-ia-1286
        imagePullPolicy: Always
        envFrom:
        - secretRef:
            name: pg-migrate-secrets
        volumeMounts:
        - name: service-account-volume
          mountPath: /app/src/connections/conversas-ai.json
          subPath: conversas-ai.json
          readOnly: true
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        # Log de saída para debug
        command: ["/bin/sh"]
        args: 
        - -c
        - |
          echo "Iniciando migração BigQuery -> PostgreSQL"
          echo "Host: $POSTGRES_HOST"
          echo "Database: $POSTGRES_DB"
          echo "Schema: $DB_SCHEMA"
          echo "Limit: $MIGRATE_LIMIT"
          echo "Truncate: $MIGRATION_DO_TRUNCATE"
          # Executar o comando principal da imagem
          python /app/migrate_bigquerydata_to_postgres.py
      volumes:
      - name: service-account-volume
        secret:
          secretName: conversas-ai-service-account
