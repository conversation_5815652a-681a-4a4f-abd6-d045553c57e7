apiVersion: v1
kind: Secret
metadata:
  name: pg-migrate-secrets
  namespace: postgres-qa
  labels:
    app: pg-migrate
type: Opaque
stringData:
  POSTGRES_HOST: "postgres-cluster-qa-primary.postgres-qa.svc"
  POSTGRES_PORT: "5432"
  POSTGRES_DB: "conversas_ai_qa"
  POSTGRES_USER: "conversas-ai-user-qa"
  POSTGRES_PASSWORD: "Sn6wUUlf62beriQc1ie9SkCK"
  DB_SCHEMA: "public"
  MIGRATE_LIMIT: "100"
  MIGRATION_DO_TRUNCATE: "true"
  GCP_BIGQUERY_DATASET: "development"
  GOOGLE_APPLICATION_CREDENTIALS: "/app/src/connections/conversas-ai.json"
---
apiVersion: v1
data:
  conversas-ai.json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  name: conversas-ai-service-account
  namespace: postgres-qa
type: Opaque
