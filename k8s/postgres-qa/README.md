# PostgreSQL QA Environment

Este diretório contém a configuração para deploy do PostgreSQL no ambiente de QA usando o **Crunchy Data Postgres Operator**.

## 📋 Pré-requisitos

- Kubernetes cluster funcionando
- `kubectl` configurado e conectado ao cluster
- Acesso à internet para baixar imagens

## 🚀 Deploy

Execute o script de deploy:

```bash
chmod +x deploy-postgres-qa.sh
./deploy-postgres-qa.sh
```

O script irá:

1. **Instalar o PGO (Postgres Operator)** da Crunchy Data
2. **Criar o namespace** `postgres-qa`
3. **Deployar o cluster PostgreSQL** com configurações otimizadas para QA
4. **Aguardar** até o cluster estar pronto

## 🔌 Conectando ao PostgreSQL

### Informações de Conexão

- **Cluster:** `postgres-cluster-qa`
- **Namespace:** `postgres-qa`
- **Database:** `conversas_ai_qa`
- **Username:** `conversas-ai-user-qa`
- **Password:** Gerada automaticamente pelo operator

### Obter Credenciais

#### 🔐 Credenciais de Administrador (Superuser)
```bash
# Listar todos os secrets disponíveis
kubectl get secrets -n postgres-qa

# Obter credenciais do superuser (postgres)
echo "=== CREDENCIAIS POSTGRESQL ADMIN ==="
echo "Host: postgres-cluster-qa-primary.postgres-qa.svc.cluster.local"
echo "Port: 5432"
echo "Superuser: $(kubectl get secret postgres-qa-secrets -n postgres-qa -o jsonpath='{.data.POSTGRES_USER}' | base64 -d)"
echo "Password: $(kubectl get secret postgres-qa-secrets -n postgres-qa -o jsonpath='{.data.POSTGRES_PASSWORD}' | base64 -d)"
echo "Database: postgres"
```

#### 👤 Credenciais do Usuário de Aplicação
```bash
# URI completa de conexão
kubectl -n postgres-qa get secret postgres-cluster-qa-pguser-conversas-ai-user-qa -o go-template="{{.data.uri | base64decode}}"

# Credenciais individuais
echo "=== CREDENCIAIS CONVERSAS-AI-USER-QA ==="
echo "Host: postgres-cluster-qa-primary.postgres-qa.svc.cluster.local"
echo "Port: 5432"
echo "User: $(kubectl get secret postgres-cluster-qa-pguser-conversas-ai-user-qa -n postgres-qa -o jsonpath='{.data.user}' | base64 -d)"
echo "Password: $(kubectl get secret postgres-cluster-qa-pguser-conversas-ai-user-qa -n postgres-qa -o jsonpath='{.data.password}' | base64 -d)"
echo "Database: $(kubectl get secret postgres-cluster-qa-pguser-conversas-ai-user-qa -n postgres-qa -o jsonpath='{.data.dbname}' | base64 -d)"
```

#### 🔧 Credenciais Alternativas do Usuário de App
```bash
# Obter credenciais do usuário de aplicação (alternativo)
echo "=== CREDENCIAIS APLICAÇÃO ALTERNATIVAS ==="
echo "Host: postgres-cluster-qa-primary.postgres-qa.svc.cluster.local"
echo "Port: 5432"
echo "User: $(kubectl get secret postgres-qa-secrets -n postgres-qa -o jsonpath='{.data.APP_USER}' | base64 -d)"
echo "Password: $(kubectl get secret postgres-qa-secrets -n postgres-qa -o jsonpath='{.data.APP_PASSWORD}' | base64 -d)"
echo "Database: $(kubectl get secret postgres-qa-secrets -n postgres-qa -o jsonpath='{.data.APP_DATABASE}' | base64 -d)"
```

### Conectar via psql

#### Conexão Direta
```bash
psql $(kubectl -n postgres-qa get secrets postgres-cluster-qa-pguser-conversas-ai-user-qa -o go-template="{{.data.uri | base64decode}}")
```

#### Conexão via Port-Forward - Admin User (postgres)

Terminal 1 (criar o port-forward):
```bash
PG_CLUSTER_PRIMARY_POD=$(kubectl get pod -n postgres-qa -o name -l postgres-operator.crunchydata.com/cluster=postgres-cluster-qa,postgres-operator.crunchydata.com/role=master)
kubectl -n postgres-qa port-forward "${PG_CLUSTER_PRIMARY_POD}" 5433:5432
```

Terminal 2 (conectar como admin):
```bash
# Conectar como superuser (postgres)
PGPASSWORD=$(kubectl get secret postgres-qa-secrets -n postgres-qa -o jsonpath='{.data.POSTGRES_PASSWORD}' | base64 -d) \
PGUSER=$(kubectl get secret postgres-qa-secrets -n postgres-qa -o jsonpath='{.data.POSTGRES_USER}' | base64 -d) \
psql -h localhost -p 5433 -d postgres

# Ou conectar diretamente ao banco da aplicação como admin
PGPASSWORD=$(kubectl get secret postgres-qa-secrets -n postgres-qa -o jsonpath='{.data.POSTGRES_PASSWORD}' | base64 -d) \
PGUSER=$(kubectl get secret postgres-qa-secrets -n postgres-qa -o jsonpath='{.data.POSTGRES_USER}' | base64 -d) \
psql -h localhost -p 5433 -d conversas_ai_qa
```

#### Conexão via Port-Forward - conversas-ai-user

Terminal 1 (criar o port-forward):
```bash
PG_CLUSTER_PRIMARY_POD=$(kubectl get pod -n postgres-qa -o name -l postgres-operator.crunchydata.com/cluster=postgres-cluster-qa,postgres-operator.crunchydata.com/role=master)
kubectl -n postgres-qa port-forward "${PG_CLUSTER_PRIMARY_POD}" 5434:5432
```

Terminal 2 (conectar como usuário de aplicação):
```bash
PG_CLUSTER_USER_SECRET_NAME=postgres-cluster-qa-pguser-conversas-ai-user-qa
PGPASSWORD=$(kubectl get secrets -n postgres-qa "${PG_CLUSTER_USER_SECRET_NAME}" -o go-template="{{.data.password | base64decode}}") \
PGUSER=$(kubectl get secrets -n postgres-qa "${PG_CLUSTER_USER_SECRET_NAME}" -o go-template="{{.data.user | base64decode}}") \
PGDATABASE=$(kubectl get secrets -n postgres-qa "${PG_CLUSTER_USER_SECRET_NAME}" -o go-template="{{.data.dbname | base64decode}}") \
psql -h localhost -p 5434
```

### Criar Schema do Usuário

Após conectar, crie um schema para o usuário (recomendado):

```sql
CREATE SCHEMA "conversas-ai-user-qa" AUTHORIZATION "conversas-ai-user-qa";
SET search_path TO "conversas-ai-user-qa";
```

## 🔧 Automação e Gerenciamento de Permissões

### Scripts de Permissões Automatizados

Este diretório inclui scripts automatizados para gerenciar permissões do PostgreSQL:

#### Setup Inicial de Permissões
```bash
# Aplicar ConfigMap com scripts de permissões
kubectl apply -f setup-permissions-configmap.yaml

# Executar Job único para corrigir permissões imediatamente
kubectl apply -f setup-permissions-job.yaml

# Configurar CronJob para manutenção automática diária
kubectl apply -f setup-permissions-cronjob.yaml
```

#### Criação de Tabelas
```bash
# Aplicar ConfigMap com schemas SQL
kubectl apply -f sql-scripts-configmap.yaml

# Executar Job de criação de tabelas
kubectl apply -f create-tables-job.yaml
```

#### Setup de Permissões via kubectl exec (Recomendado)
```bash
# Aplicar ConfigMap com script SQL parametrizado
kubectl apply -f sql-permissions-configmap.yaml

# Executar Job que usa kubectl exec diretamente no pod PostgreSQL
kubectl apply -f setup-permissions-exec-job.yaml

# Monitorar execução
kubectl logs -f job/setup-permissions-exec-job -n postgres-qa
```

### 🔧 Personalização para Outros Ambientes

O Job `setup-permissions-exec-job.yaml` usa variáveis de ambiente que podem ser facilmente personalizadas para outros clusters:

```yaml
# Variáveis configuráveis no Job
env:
- name: POSTGRES_CLUSTER_NAMESPACE
  value: "postgres-qa"                    # Alterar para seu namespace
- name: POSTGRES_CLUSTER_NAME
  value: "postgres-cluster-qa"            # Alterar para seu cluster
- name: POSTGRES_ADMIN_USER
  value: "postgres"                       # Usuário admin (normalmente postgres)
- name: POSTGRES_APP_USER
  value: "conversas-ai-user-qa"           # Alterar para seu usuário de app
- name: POSTGRES_APP_PASSWORD
  value: "Sn6wUUlf62beriQc1ie9SkCK"      # Alterar para sua senha
- name: POSTGRES_APP_DATABASE
  value: "conversas_ai_qa"                # Alterar para seu banco
```

#### Exemplo para Ambiente de Produção:
```bash
# Copiar o Job e editar as variáveis
cp setup-permissions-exec-job.yaml setup-permissions-exec-job-prod.yaml

# Editar as variáveis:
# POSTGRES_CLUSTER_NAMESPACE: "postgres-prod"
# POSTGRES_CLUSTER_NAME: "postgres-cluster-prod"
# POSTGRES_APP_USER: "app-user-prod"
# POSTGRES_APP_DATABASE: "app_database_prod"

# Aplicar no ambiente de produção
kubectl apply -f setup-permissions-exec-job-prod.yaml
```

### Monitoramento de Jobs
```bash
# Verificar status dos Jobs
kubectl get jobs -n postgres-qa

# Verificar status do CronJob
kubectl get cronjobs -n postgres-qa

# Logs do último Job de permissões
kubectl logs -l job=setup-permissions-job -n postgres-qa

# Logs do Job de criação de tabelas
kubectl logs -l app=create-tables -n postgres-qa
```

### Teste de Conectividade com Container Temporário
```bash
# Criar container temporário para testar conexão
kubectl run postgres-client --rm -it --image=postgres:15-alpine --namespace=postgres-qa --restart=Never -- sh

# Dentro do container, usar as credenciais:
export PGHOST="postgres-cluster-qa-primary.postgres-qa.svc.cluster.local"
export PGPORT="5432"
export PGUSER="conversas-ai-user-qa"
export PGPASSWORD="$(kubectl get secret postgres-cluster-qa-pguser-conversas-ai-user-qa -n postgres-qa -o jsonpath='{.data.password}' | base64 -d)"
export PGDATABASE="conversas_ai_qa"

# Testar conexão
pg_isready -h $PGHOST -p $PGPORT -U $PGUSER -d $PGDATABASE
psql -h $PGHOST -p $PGPORT -U $PGUSER -d $PGDATABASE -c "SELECT current_user, current_database();"
```

## 📊 Monitoramento

### Status do Cluster
```bash
kubectl -n postgres-qa get postgrescluster
```

### Logs
```bash
# Logs do cluster
kubectl -n postgres-qa logs -l postgres-operator.crunchydata.com/cluster=postgres-cluster-qa

# Logs do operator
kubectl -n postgres-operator logs -l postgres-operator.crunchydata.com/control-plane=postgres-operator
```

### Recursos
```bash
kubectl -n postgres-qa get all
```

### Eventos
```bash
kubectl -n postgres-qa get events --sort-by='.firstTimestamp'
```

## 🗂️ Backups

O cluster está configurado com backup automático usando pgBackRest:

- **Retenção Full Backup:** 3 dias
- **Retenção Differential:** 7 dias
- **Storage:** Volume persistente de 5Gi

### Verificar Backups
```bash
kubectl -n postgres-qa get pgbackrest
```

## 🔧 Configurações do Cluster

### Recursos
- **CPU:** 250m request, 1000m limit
- **Memória:** 512Mi request, 2Gi limit
- **Storage:** 10Gi para dados, 5Gi para backups

### PostgreSQL
- **Versão:** 15
- **Max Connections:** 100
- **Shared Buffers:** 256MB
- **Effective Cache Size:** 1536MB
- **Timezone:** America/Sao_Paulo

## 🛠️ Troubleshooting

### Cluster não inicia
```bash
# Verificar status detalhado
kubectl -n postgres-qa describe postgrescluster postgres-cluster-qa

# Verificar events
kubectl -n postgres-qa get events --sort-by='.firstTimestamp'

# Verificar logs dos pods
kubectl -n postgres-qa logs -l postgres-operator.crunchydata.com/cluster=postgres-cluster-qa
```

### Problemas de Storage
```bash
# Verificar PVCs
kubectl -n postgres-qa get pvc

# Verificar storage classes disponíveis
kubectl get storageclass
```

Se a storage class `standard` não existir, edite o arquivo `postgres-cluster-qa.yaml` e altere para uma storage class disponível no seu cluster.

### Problemas de Permissões

#### Erro: "permission denied for schema public"
```bash
# Executar Job de correção de permissões
kubectl delete job setup-permissions-job -n postgres-qa --ignore-not-found
kubectl apply -f setup-permissions-job.yaml

# Verificar logs do Job
kubectl logs -l job=setup-permissions-job -n postgres-qa
```

#### Verificar Permissões Manualmente
```bash
# Conectar como superuser e verificar permissões
kubectl exec -it postgres-cluster-qa-instance1-*-0 -n postgres-qa -c database -- \
  psql -U postgres -d conversas_ai_qa -c "
    SELECT schema_name, schema_owner 
    FROM information_schema.schemata 
    WHERE schema_name = 'public';
  "

# Verificar privilégios do usuário
kubectl exec -it postgres-cluster-qa-instance1-*-0 -n postgres-qa -c database -- \
  psql -U postgres -c "
    SELECT rolname, rolsuper, rolcreatedb, rolcreaterole, rolcanlogin 
    FROM pg_roles 
    WHERE rolname = 'conversas-ai-user-qa';
  "
```

#### Reset Manual de Permissões (Emergência)
```bash
# Conectar como superuser e corrigir permissões manualmente
kubectl exec -it postgres-cluster-qa-instance1-*-0 -n postgres-qa -c database -- \
  psql -U postgres -d conversas_ai_qa -c "
    GRANT ALL PRIVILEGES ON SCHEMA public TO \"conversas-ai-user-qa\";
    GRANT CREATE ON SCHEMA public TO \"conversas-ai-user-qa\";
    GRANT USAGE ON SCHEMA public TO \"conversas-ai-user-qa\";
    ALTER SCHEMA public OWNER TO \"conversas-ai-user-qa\";
  "
```

## 🗑️ Cleanup

Para remover o cluster PostgreSQL:

```bash
# Apenas o cluster (mantém o operator)
kubectl -n postgres-qa delete postgrescluster postgres-cluster-qa

# Remover namespace completo
kubectl delete namespace postgres-qa

# Remover o operator (se necessário)
kubectl delete namespace postgres-operator
```

## 📚 Documentação

- [Crunchy Data Postgres Operator](https://postgres-operator.readthedocs.io/)
- [PostgreSQL Official Documentation](https://www.postgresql.org/docs/)
- [Kubernetes Documentation](https://kubernetes.io/docs/)

# PostgreSQL QA Environment

This directory contains the PostgreSQL QA environment configuration for the Kubernetes cluster.

## Installation

### Quick Deploy
```bash
# Run the complete deployment script
./deploy-postgres-qa.sh
```

### Manual Installation Steps

1. **Install PostgreSQL Operator CRDs**
```bash
# Install Crunchy PostgreSQL Operator CRDs
kubectl apply --server-side -k https://github.com/CrunchyData/postgres-operator/config/default

# Verify CRDs are installed
kubectl get crd | grep postgres
```

2. **Deploy PostgreSQL Operator**
```bash
# Create operator namespace
kubectl create namespace postgres-operator

# Deploy the operator
kubectl apply -f postgres-operator-deployment.yaml

# Wait for operator to be ready
kubectl wait --for=condition=available deployment/postgres-operator -n postgres-operator --timeout=120s
```

3. **Deploy PostgreSQL QA Environment**
```bash
# Apply all configurations
kubectl apply -f namespace.yaml
kubectl apply -f secrets.yaml
kubectl apply -f postgres-cluster-qa.yaml
kubectl apply -f service.yaml
```

## Commands to Verify PostgreSQL QA Resources

### Check Namespace Status
```bash
# List all namespaces
kubectl get namespaces

# Check if postgres-qa namespace exists
kubectl get namespace postgres-qa
```

### Check PostgreSQL Services
```bash
# List all services in postgres-qa namespace
kubectl get services -n postgres-qa

# Get detailed service information
kubectl describe services -n postgres-qa
```

### Check PostgreSQL Pods
```bash
# List all pods in postgres-qa namespace
kubectl get pods -n postgres-qa

# Get detailed pod information
kubectl describe pods -n postgres-qa

# Check pod logs (replace <pod-name> with actual pod name)
kubectl logs -n postgres-qa <pod-name>
```

### Check All Resources
```bash
# List all resources in postgres-qa namespace
kubectl get all -n postgres-qa

# Get detailed information about all resources
kubectl describe all -n postgres-qa
```

### Check PostgreSQL Custom Resources (if using an operator)
```bash
# List PostgreSQL custom resource definitions
kubectl get crd | grep postgres

# List PostgreSQL clusters (if using postgres-operator)
kubectl get postgrescluster -n postgres-qa

# List PostgreSQL instances (alternative naming)
kubectl get postgresql -n postgres-qa
```

### Check Resource Usage
```bash
# Check node resource usage
kubectl top nodes

# Check pod resource usage in postgres-qa
kubectl top pods -n postgres-qa
```

### Port Forward for Local Access
```bash
# Port forward to primary PostgreSQL service
kubectl port-forward -n postgres-qa service/postgres-qa-primary 5432:5432

# Port forward to replica PostgreSQL service
kubectl port-forward -n postgres-qa service/postgres-qa-replica 5433:5432
```

### Troubleshooting Commands
```bash
# Check events in postgres-qa namespace
kubectl get events -n postgres-qa --sort-by='.lastTimestamp'

# Check persistent volumes
kubectl get pv

# Check persistent volume claims in postgres-qa
kubectl get pvc -n postgres-qa

# Check storage classes
kubectl get storageclass
```
