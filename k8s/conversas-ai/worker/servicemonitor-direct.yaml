apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: conversas-ai-worker-direct-monitoring
  namespace: monitoring  # namespace do Prometheus Operator
  labels:
    app: conversas-ai-worker-monitoring
    component: worker
    type: direct
spec:
  selector:
    matchLabels:
      app: conversas-ai-worker
  namespaceSelector:
    matchNames:
    - conversas-ai  # namespace do conversas-ai-worker
  endpoints:
  # Endpoint direto para métricas do actuator
  - port: actuator  # porta do service conversas-ai-worker-service
    interval: 30s
    path: /actuator/metrics
    scheme: http
  # Endpoint para health check (opcional)
  - port: actuator
    interval: 30s
    path: /health_check
    scheme: http
    metricRelabelings:
    - sourceLabels: [__name__]
      regex: 'up'
      targetLabel: __name__
      replacement: 'conversas_ai_worker_up'
