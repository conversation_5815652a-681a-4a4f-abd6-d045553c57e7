apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: conversas-ai-worker-simple
  namespace: monitoring
  labels:
    app: conversas-ai-worker-monitoring
    component: worker
spec:
  selector:
    matchLabels:
      app: conversas-ai-worker
  namespaceSelector:
    matchNames:
    - conversas-ai
  endpoints:
  # Tentar endpoint padrão de métricas Prometheus
  - port: actuator
    interval: 30s
    path: /metrics
    scheme: http
  # Fallback para endpoint customizado se existir
  - port: actuator
    interval: 30s
    path: /actuator/metrics
    scheme: http
  # Health check como métrica adicional
  - port: actuator
    interval: 30s
    path: /health_check
    scheme: http
    metricRelabelings:
    - sourceLabels: [__name__]
      regex: 'up'
      targetLabel: __name__
      replacement: 'conversas_ai_worker_health'
