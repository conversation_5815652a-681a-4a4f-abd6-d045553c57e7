# HPA Configuration for Conversas AI Worker

## 📋 Visão Geral

Este documento explica as configurações de HPA (Horizontal Pod Autoscaler) disponíveis para o worker do Conversas AI, substituindo a configuração HTTP que não estava funcionando.

## 🔧 Configurações Disponíveis

### 1. **hpa.yaml** - Configuração Completa com Prometheus
- **Uso**: Produção com monitoramento Prometheus ativo
- **Triggers**: Redis + Prometheus + CPU + Memory
- **Vantagens**: Métricas customizadas mais precisas
- **Requisitos**: Prometheus Server configurado

### 2. **hpa-simple.yaml** - Configuração Simplificada
- **Uso**: Ambientes sem Prometheus ou para testes
- **Triggers**: Múl<PERSON>las filas Redis + CPU + Memory
- **Vantagens**: Não depende de infraestrutura externa
- **Requisitos**: Apenas Redis

## 🚀 Como Aplicar

### Opção 1: Configuração com Prometheus (Recomendada)
```bash
# Aplicar a configuração principal
kubectl apply -f k8s/conversas-ai/worker/hpa.yaml

# Verificar status
kubectl get scaledobject conversas-ai-worker -n conversas-ai
```

### Opção 2: Configuração Simplificada
```bash
# Remover configuração atual se existir
kubectl delete scaledobject conversas-ai-worker -n conversas-ai

# Aplicar configuração simplificada
kubectl apply -f k8s/conversas-ai/worker/hpa-simple.yaml

# Verificar status
kubectl get scaledobject conversas-ai-worker-simple -n conversas-ai
```

## 📊 Principais Mudanças

### ✅ Melhorias Implementadas:
1. **Triggers mais confiáveis**: Removido HTTP, adicionado Prometheus
2. **Scaling mais conservador**: Tempos de estabilização aumentados
3. **Múltiplas filas Redis**: Monitoramento de várias filas simultaneamente
4. **Thresholds otimizados**: Valores mais responsivos e estáveis
5. **Mínimo de réplicas**: Aumentado para 2 para garantir disponibilidade

### 🔄 Comportamento de Scaling:

#### Scale Up (Crescimento):
- **Tempo de estabilização**: 60 segundos
- **Políticas**: Máximo 50% ou 3 pods por minuto
- **Triggers**: Ativa com poucos itens nas filas

#### Scale Down (Redução):
- **Tempo de estabilização**: 600 segundos (10 min)
- **Políticas**: Máximo 15% ou 1 pod por 5 minutos
- **Comportamento**: Mais conservador para evitar oscilações

## 🎯 Triggers Configurados

### Configuração Principal (hpa.yaml):
1. **Prometheus custom metrics**: Threshold 15 mensagens
2. **CPU**: 70% utilização
3. **Memory**: 85% utilização
4. **Redis messages_received**: Threshold 25 itens
5. **Redis messages_to_send**: Threshold 20 itens

### Configuração Simplificada (hpa-simple.yaml):
1. **Redis messages_received**: Threshold 20 itens
2. **Redis messages_to_send**: Threshold 20 itens
3. **CPU**: 65% utilização
4. **Memory**: 80% utilização

## 🔍 Monitoramento

### Comandos Úteis:
```bash
# Status do ScaledObject
kubectl describe scaledobject conversas-ai-worker -n conversas-ai

# Métricas atuais
kubectl get hpa -n conversas-ai

# Logs do KEDA
kubectl logs -n keda-system -l app=keda-operator

# Tamanho das filas Redis
redis-cli -h redis-service.redis.svc.cluster.local LLEN messages_received
redis-cli -h redis-service.redis.svc.cluster.local LLEN messages_to_send

# Pods ativos
kubectl get pods -n conversas-ai -l app=conversas-ai-worker
```

## ⚠️ Troubleshooting

### Problema: Scaling não funciona
```bash
# Verificar eventos do KEDA
kubectl describe scaledobject conversas-ai-worker -n conversas-ai

# Verificar logs do KEDA
kubectl logs -n keda-system -l app=keda-operator --tail=100

# Verificar conectividade Redis
kubectl exec -it deployment/conversas-ai-worker -n conversas-ai -- redis-cli -h redis-service.redis.svc.cluster.local ping
```

### Problema: Prometheus metrics não funcionam
```bash
# Verificar se Prometheus está acessível
kubectl get svc -n monitoring | grep prometheus

# Testar query manualmente
curl "http://prometheus-server.monitoring.svc.cluster.local:80/api/v1/query?query=conversas_ai_worker_mensagens_a_processar_agora"

# Usar configuração simplificada como fallback
kubectl apply -f k8s/conversas-ai/worker/hpa-simple.yaml
```

## 🎯 Recomendações

1. **Produção**: Use `hpa.yaml` com Prometheus
2. **Desenvolvimento/QA**: Use `hpa-simple.yaml`
3. **Monitoramento**: Configure alertas para filas Redis muito grandes
4. **Ajustes**: Monitore por 1-2 semanas e ajuste thresholds conforme necessário
