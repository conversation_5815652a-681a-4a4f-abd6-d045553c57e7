apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: conversas-ai-worker
  namespace: conversas-ai
  labels:
    app: conversas-ai
    component: worker
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: conversas-ai-worker
  pollingInterval: 10                # Reduzido para ser menos agressivo
  cooldownPeriod: 300                # Aumentado para 5min para maior estabilidade
  minReplicaCount: 2                 # Mínimo aumentado para garantir disponibilidade
  maxReplicaCount: 40
  advanced:
    horizontalPodAutoscalerConfig:
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 600  # 10min para scale down mais conservador
          policies:
          - type: Percent
            value: 15                      # Redução mais gradual
            periodSeconds: 300
          - type: Pods
            value: 1                       # Máximo 1 pod por vez
            periodSeconds: 300
          selectPolicy: Min
        scaleUp:
          stabilizationWindowSeconds: 60   # 1min para scale up mais rápido
          policies:
          - type: Percent
            value: 50                      # Crescimento mais controlado
            periodSeconds: 60
          - type: Pods
            value: 3                       # Máximo 3 pods por vez
            periodSeconds: 60
          selectPolicy: Max
  triggers:
  # Trigger principal: Redis queue length
  - type: redis
    metadata:
      address: redis-service.redis.svc.cluster.local:6379
      databaseIndex: "0"
      listName: delayed_messages
      listLength: "20"                     # Reduzido para ser mais responsivo
      activationListLength: "5"            # Ativa scaling com menos itens

  # Trigger secundário: Prometheus metrics customizadas
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:80
      metricName: conversas_ai_worker_mensagens_a_processar_agora
      threshold: '15'                      # Escala quando > 15 mensagens para processar
      query: sum(conversas_ai_worker_mensagens_a_processar_agora)

  # Trigger de CPU - mais conservador
  - type: cpu
    metadata:
      type: Utilization
      value: "70"                          # Reduzido de 75% para 70%

  # Trigger de Memory - mais conservador
  - type: memory
    metadata:
      type: Utilization
      value: "85"                          # Reduzido de 90% para 85%

  # Trigger adicional: Redis connection count (para detectar alta carga)
  - type: redis
    metadata:
      address: redis-service.redis.svc.cluster.local:6379
      databaseIndex: "0"
      listName: messages_received          # Fila de mensagens recebidas
      listLength: "30"
      activationListLength: "10"
