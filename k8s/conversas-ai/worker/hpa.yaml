apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: conversas-ai-worker
  namespace: conversas-ai
  labels:
    app: conversas-ai
    component: worker
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: conversas-ai-worker
  pollingInterval: 5                 
  cooldownPeriod: 180             
  minReplicaCount: 1                 
  maxReplicaCount: 40                
  advanced:
    horizontalPodAutoscalerConfig:
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 300 
          policies:
          - type: Percent
            value: 20  
            periodSeconds: 180  
          - type: Pods
            value: 2   
            periodSeconds: 180
          selectPolicy: Min 
        scaleUp:
          stabilizationWindowSeconds: 30  
          policies:
          - type: Percent
            value: 100 
            periodSeconds: 30 
          - type: Pods
            value: 4   
            periodSeconds: 30
          selectPolicy: Max  
  triggers:
  - type: redis
    metadata:
      address: redis-service.redis.svc.cluster.local:6379
      databaseIndex: "0"
      listName: delayed_messages    
      listLength: "25"              
      activationListLength: "10"   
  - type: cpu
    metadata:
      type: Utilization
      value: "75"                   
  - type: memory
    metadata:
      type: Utilization
      value: "90"  
