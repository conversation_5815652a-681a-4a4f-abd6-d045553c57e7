apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: conversas-ai-worker-monitoring
  namespace: monitoring  # namespace do Prometheus Operator
  labels:
    app: conversas-ai-worker-monitoring
    component: worker
spec:
  selector:
    matchLabels:
      app: conversas-ai-worker
  namespaceSelector:
    matchNames:
    - conversas-ai  # namespace do conversas-ai-worker
  endpoints:
  - port: metrics  # porta para métricas customizadas (json-exporter)
    interval: 15s
    path: /probe
    scheme: http
    params:
      target: ['http://localhost:8081/actuator/query/components.DelayedQueueWorker.common']
      module: ['default']
