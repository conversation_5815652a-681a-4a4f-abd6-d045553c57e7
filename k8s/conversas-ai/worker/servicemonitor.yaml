apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: conversas-ai-worker-monitoring
  namespace: monitoring  # namespace do Prometheus Operator
  labels:
    app: conversas-ai-worker-monitoring
    component: worker
spec:
  selector:
    matchLabels:
      app: conversas-ai-worker
  namespaceSelector:
    matchNames:
    - conversas-ai  # namespace do conversas-ai-worker
  endpoints:
  - port: actuator  # porta do service conversas-ai-worker-service
    interval: 15s
    path: /actuator/query/components.DelayedQueueWorker.common
    scheme: http
