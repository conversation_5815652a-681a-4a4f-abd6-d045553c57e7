apiVersion: apps/v1
kind: Deployment
metadata:
  name: conversas-ai-worker-json-exporter
  namespace: conversas-ai
  labels:
    app: conversas-ai-worker
    component: json-exporter
spec:
  replicas: 1
  selector:
    matchLabels:
      app: conversas-ai-worker
      component: json-exporter
  template:
    metadata:
      labels:
        app: conversas-ai-worker
        component: json-exporter
    spec:
      containers:
      - name: json-exporter
        image: prometheuscommunity/json-exporter:latest
        ports:
        - containerPort: 7979
          name: metrics
        args:
        - --config.file=/etc/json-exporter/config.yml
        volumeMounts:
        - name: config
          mountPath: /etc/json-exporter
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 100m
            memory: 128Mi
      volumes:
      - name: config
        configMap:
          name: conversas-ai-worker-json-exporter-config
---
apiVersion: v1
kind: Service
metadata:
  name: conversas-ai-worker-json-exporter-service
  namespace: conversas-ai
  labels:
    app: conversas-ai-worker
    component: json-exporter
spec:
  selector:
    app: conversas-ai-worker
    component: json-exporter
  ports:
  - port: 7979
    targetPort: 7979
    name: metrics
  type: ClusterIP
