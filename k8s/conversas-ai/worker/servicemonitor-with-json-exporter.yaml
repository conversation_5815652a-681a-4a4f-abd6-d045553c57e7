apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: conversas-ai-worker-json-exporter
  namespace: monitoring
  labels:
    app: conversas-ai-worker-monitoring
    component: json-exporter
spec:
  selector:
    matchLabels:
      app: conversas-ai-worker
      component: json-exporter
  namespaceSelector:
    matchNames:
    - conversas-ai
  endpoints:
  - port: metrics
    interval: 30s
    path: /probe
    scheme: http
    params:
      target: ['http://conversas-ai-worker-service.conversas-ai.svc.cluster.local:8081/actuator/query/components.DelayedQueueWorker.common']
      module: ['default']
