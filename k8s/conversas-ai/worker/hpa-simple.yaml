apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: conversas-ai-worker-simple
  namespace: conversas-ai
  labels:
    app: conversas-ai
    component: worker
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: conversas-ai-worker
  pollingInterval: 15                # Menos agressivo
  cooldownPeriod: 300                # 5 minutos
  minReplicaCount: 2                 
  maxReplicaCount: 40                
  advanced:
    horizontalPodAutoscalerConfig:
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 600  # 10min
          policies:
          - type: Percent
            value: 20  
            periodSeconds: 300  
          - type: Pods
            value: 1   
            periodSeconds: 300
          selectPolicy: Min 
        scaleUp:
          stabilizationWindowSeconds: 60   # 1min
          policies:
          - type: Percent
            value: 100 
            periodSeconds: 60 
          - type: Pods
            value: 5   
            periodSeconds: 60
          selectPolicy: Max  
  triggers:
  # Trigger principal: Redis messages_received queue
  - type: redis
    metadata:
      address: redis-service.redis.svc.cluster.local:6379
      databaseIndex: "0"
      listName: messages_received
      listLength: "20"               # Threshold mais baixo
      activationListLength: "3"      # Ativa com menos itens

  # Trigger secundário: Redis messages_to_send queue
  - type: redis
    metadata:
      address: redis-service.redis.svc.cluster.local:6379
      databaseIndex: "0"
      listName: messages_to_send     
      listLength: "20"               
      activationListLength: "5"      
  
  # CPU trigger - mais conservador
  - type: cpu
    metadata:
      type: Utilization
      value: "65"                    # Mais baixo para antecipar carga
  
  # Memory trigger - mais conservador
  - type: memory
    metadata:
      type: Utilization
      value: "80"                    # Mais baixo para antecipar carga
