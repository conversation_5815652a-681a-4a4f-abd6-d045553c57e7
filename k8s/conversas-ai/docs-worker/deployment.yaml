apiVersion: apps/v1
kind: Deployment
metadata:
  name: conversas-ai-docs-worker
  namespace: conversas-ai
  labels:
    app: conversas-ai-docs-worker
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 50%        # Aumentado de 25% para 50% - permite deploy mais rápido
      maxUnavailable: 0%   # Reduzido de 25% para 0% - garante disponibilidade durante deploy
  revisionHistoryLimit: 5   # Reduzido de 10 para 5 - economiza espaço
  progressDeadlineSeconds: 900  # Aumentado de 600s para 900s - docs processing pode demorar mais para inicializar
  selector:
    matchLabels:
      app: conversas-ai-docs-worker
  template:
    metadata:
      labels:
        app: conversas-ai-docs-worker
        version: v1
        component: docs-worker
        environment: production
    spec:
      nodeSelector:
        pool: np-private
      containers:
      - name: docs-worker
        image: registry.gitlab.com/plataformazw/ia/orion/docs_worker:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        envFrom:
        - configMapRef:
            name: conversas-ai-docs-worker-config
        livenessProbe:
          httpGet:
            path: /health_check
            port: 8080
          initialDelaySeconds: 30   # Aumentado de 5s para 30s - docs worker demora mais para inicializar
          periodSeconds: 30         # Aumentado de 10s para 30s - menos overhead
          timeoutSeconds: 15        # Aumentado de 10s para 15s - docs processing pode demorar
          failureThreshold: 3
          successThreshold: 1
        readinessProbe:
          httpGet:
            path: /health_check
            port: 8080
          initialDelaySeconds: 15   # Aumentado de 5s para 15s - aguarda inicialização
          periodSeconds: 10         # Mantido em 10s para detecção rápida
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        resources:
          requests:
            cpu: 750m        # Aumentado de 500m - docs processing é CPU intensivo
            memory: 1Gi      # Aumentado de 512Mi para 1Gi - processamento de documentos requer mais memória
          limits:
            cpu: 2000m       # Ajustado de 2024m para 2000m - permite picos de processamento
            memory: 4Gi      # Reduzido de 8Gi para 4Gi - mais realista mas suficiente para docs grandes
      imagePullSecrets:
      - name: gitlab-registry-secret
