apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: conversas-ai-docs-worker-hpa
  namespace: conversas-ai
  labels:
    app: conversas-ai
    component: docs-worker
    environment: production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: conversas-ai-docs-worker
  minReplicas: 1
  maxReplicas: 5  # Aumentado para permitir maior escalabilidade
  metrics:
  # CPU Utilization - baseado no request de 750m do deployment
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70  # 70% de 750m = 525m (antes de escalar)
  # Memory Utilization - baseado no request de 1Gi do deployment
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80  # 80% do request de 1Gi = ~820Mi
  # Métrica customizada baseada em requisições por segundo (se disponível)
  # - type: Pods
  #   pods:
  #     metric:
  #       name: http_requests_per_second
  #     target:
  #       type: AverageValue
  #       averageValue: "10"  # 10 RPS por pod
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 600  # Reduzido de 900s para 600s (10 min)
      policies:
      - type: Percent
        value: 25  # Reduzido de 50% para 25% - scale down mais conservador
        periodSeconds: 300  # Aumentado de 180s para 300s
      - type: Pods
        value: 1
        periodSeconds: 300  # Aumentado de 180s para 300s
      selectPolicy: Min  # Usa a política mais conservadora
    scaleUp:
      stabilizationWindowSeconds: 60  # Reduzido de 300s para 60s - scale up mais rápido
      policies:
      - type: Percent
        value: 100  # Permite dobrar o número de pods
        periodSeconds: 60
      - type: Pods
        value: 2  # Aumentado de 1 para 2 - permite adicionar 2 pods por vez
        periodSeconds: 60
      selectPolicy: Max  # Usa a política mais agressiva para scale up
