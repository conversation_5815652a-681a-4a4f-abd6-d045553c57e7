apiVersion: v1
kind: ConfigMap
metadata:
  name: conversas-ai-docs-worker-config-qa
  namespace: conversas-ai
  labels:
    app: conversas-ai-docs-worker-qa
data:
  TZ: "America/Sao_Paulo"
  SMTP_USERNAME: "<EMAIL>"
  SMTP_PASSWORD: "a99162af848595da80cdecf7ca9b1ca2-0920befd-9ec2e0a9"
  REDIS_URL: "redis://redis-service.redis.svc.cluster.local:6379/2"
  FLASK_ENV: production
  HEALTHCHECK_PORT: "8080"
  MODE: docs_worker
  QDRANT_HOST: "qdrant.qdrant.svc.cluster.local"
  QDRANT_PORT: "6333"
  JAEGER_HOST: "jaeger-service.jaeger.svc.cluster.local"
  JAEGER_PORT: "6831"
  DATABASE: "postgres"
  POSTGRES_HOST: "postgres-cluster-qa-primary.postgres-qa.svc"
  POSTGRES_PORT: "5432"
  POSTGRES_USER: conversas-ai-user-qa
  POSTGRES_PASSWORD: Sn6wUUlf62beriQc1ie9SkCK
  POSTGRES_DB: conversas_ai_qa
  DB_SCHEMA: public