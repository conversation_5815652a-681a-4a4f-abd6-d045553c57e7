apiVersion: v1
kind: ConfigMap
metadata:
  name: conversas-ai-scheduler-config
  namespace: conversas-ai
  labels:
    app: conversas-ai-scheduler
data:
  GCP_BIGQUERY_PROJECT_ID: "conversas-ai"
  GCP_BIGQUERY_DATASET: "production"
  HEALTHCHECK_PORT: "8081"
  MODE: worker
  REDIS_URL: "redis://redis-service.redis.svc.cluster.local:6379/0"
  RQ_QUEUE_NAME: scheduler_queue
  QDRANT_HOST: "qdrant.qdrant.svc.cluster.local"
  QDRANT_PORT: "6333"
  DATABASE: bigquery