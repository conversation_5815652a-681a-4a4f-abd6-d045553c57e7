apiVersion: v1
kind: ConfigMap
metadata:
  name: conversas-ai-scheduler-qa-config
  namespace: conversas-ai
  labels:
    app: conversas-ai-scheduler-qa-config
data:
  GCP_BIGQUERY_PROJECT_ID: "conversas-ai"
  GCP_BIGQUERY_DATASET: "production"
  HEALTHCHECK_PORT: "8081"
  MODE: worker
  REDIS_URL: "redis://redis-service.redis.svc.cluster.local:6379/2"
  RQ_QUEUE_NAME: scheduler_queue
  QDRANT_HOST: "qdrant.qdrant.svc.cluster.local"
  QDRANT_PORT: "6333"
  DATABASE: "postgres"
  POSTGRES_HOST: "postgres-cluster-qa-primary.postgres-qa.svc"
  POSTGRES_PORT: "5432"
  POSTGRES_USER: conversas-ai-user-qa
  POSTGRES_PASSWORD: Sn6wUUlf62beriQc1ie9SkCK
  POSTGRES_DB: conversas_ai_qa
  DB_SCHEMA: public