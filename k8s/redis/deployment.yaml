apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: redis
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis
        ports:
        - containerPort: 6379
        resources:
          requests:
            cpu: "1000m"     
            memory: "6Gi"
          limits:
            cpu: "2000m"     
            memory: "12Gi" 
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /usr/local/etc/redis/redis.conf
          subPath: redis.conf
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-pvc
      - name: redis-config
        configMap:
          name: redis-config
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redisinsight
  namespace: redis
  labels:
    app: redisinsight
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redisinsight
  template:
    metadata:
      labels:
        app: redisinsight
    spec:
      securityContext:
        fsGroup: 1001
      nodeSelector:
        pool: np-private
      containers:
      - name: redisinsight
        image: redislabs/redisinsight:latest
        ports:
        - containerPort: 5540
        resources:
          requests:
            cpu: "500m"
            memory: "512Mi"
          limits:
            cpu: "1000m"
            memory: "1Gi"
        volumeMounts:
        - name: redisinsight-pvc
          mountPath: /data
      volumes:
      - name: redisinsight-pvc
        emptyDir: {}