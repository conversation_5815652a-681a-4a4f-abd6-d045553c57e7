apiVersion: apps/v1
kind: Deployment
metadata:
  name: routellm
  labels:
    app: routellm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: routellm
  template:
    metadata:
      labels:
        app: routellm
    spec:
      nodeSelector:
        pool: np-private
      containers:
      - name: routellm
        image: registry.gitlab.com/plataformazw/ia/orion/routellm:main
        ports:
        - containerPort: 8082
        envFrom:
        - configMapRef:
            name: routellm-config
        resources:
          requests:
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "500m"
            memory: "512Mi"
        livenessProbe:
          httpGet:
            path: /health_check
            port: 8082
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health_check
            port: 8082
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
---
apiVersion: v1
kind: Service
metadata:
  name: routellm-service
  labels:
    app: routellm
spec:
  selector:
    app: routellm
  ports:
  - port: 8082
    targetPort: 8082
  type: ClusterIP
