#!/bin/bash

set -e

echo "🚀 Deploying PostgreSQL PRODUCTION environment using Crunchy Data Postgres Operator..."

# Verificar se kubectl está disponível
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl não encontrado. Por favor, instale kubectl primeiro."
    exit 1
fi

# Verificar se estamos no contexto correto (produção)
echo "⚠️  ATENÇÃO: Este script irá deployar o PostgreSQL em PRODUÇÃO!"
echo "   Contexto atual do kubectl:"
kubectl config current-context
echo ""
read -p "Tem certeza que deseja continuar? (digite 'PRODUÇÃO' para confirmar): " confirmation
if [ "$confirmation" != "PRODUÇÃO" ]; then
    echo "❌ Deploy cancelado."
    exit 1
fi

# Step 1: Instalar PGO (Postgres Operator) seguindo documentação oficial
echo "📦 Installing PGO, the Postgres Operator..."
echo "   Creating postgres-operator namespace..."
kubectl apply -k https://github.com/CrunchyData/postgres-operator-examples/kustomize/install/namespace

echo "   Installing PGO operator..."
kubectl apply --server-side -k https://github.com/CrunchyData/postgres-operator-examples/kustomize/install/default

# Aguardar o operator ficar pronto
echo "   Waiting for operator to be ready..."
echo "   This may take a few minutes..."
kubectl -n postgres-operator wait --for=condition=Ready pod --selector=postgres-operator.crunchydata.com/control-plane=postgres-operator --timeout=300s

echo "   ✅ PGO Operator is running!"

# Verificar status do operator
echo "📋 Checking PGO status:"
kubectl -n postgres-operator get pods --selector=postgres-operator.crunchydata.com/control-plane=postgres-operator --field-selector=status.phase=Running

# Step 2: Criar namespace para produção
echo "📁 Creating PRODUCTION namespace..."
kubectl apply -f namespace.yaml

# Step 3: Aplicar cluster PostgreSQL
echo "🐘 Creating PostgreSQL cluster for PRODUCTION..."
echo "   ⚠️  IMPORTANTE: Verifique se as senhas foram alteradas no arquivo pg-migrate-secrets.yaml"
echo "   ⚠️  IMPORTANTE: Este cluster terá 2 réplicas e recursos de produção"
kubectl apply -f postgres-cluster.yaml

# Aguardar o cluster ficar pronto
echo "   Waiting for PostgreSQL cluster to be ready..."
echo "   This may take several minutes while images are pulled and cluster is initialized..."
echo "   Production cluster with 2 replicas may take longer than QA..."
kubectl -n postgres wait --for=condition=PGBackRestReplicaRepoReady postgrescluster/postgres-cluster --timeout=900s

echo "✅ PostgreSQL PRODUCTION deployment completed!"

echo ""
echo "📋 Checking deployment status..."
kubectl get all -n postgres

echo ""
echo "🔍 PostgreSQL Cluster Status:"
kubectl -n postgres get postgrescluster

echo ""
echo "📝 Detailed cluster information:"
kubectl -n postgres describe postgrescluster postgres-cluster

echo ""
echo "🔌 Connection Information:"
echo "========================================"
echo "Cluster Name: postgres-cluster"
echo "Namespace: postgres"
echo "Database: conversas_ai"
echo "Username: conversas-ai-user"
echo ""
echo "🔑 To get connection details:"
echo "   # Get all connection info:"
echo '   kubectl -n postgres get secret postgres-cluster-pguser-conversas-ai-user -o go-template="{{.data.uri | base64decode}}"'
echo ""
echo "   # Get just the password:"
echo '   kubectl -n postgres get secret postgres-cluster-pguser-conversas-ai-user -o go-template="{{.data.password | base64decode}}"'
echo ""
echo "🔌 Connect via psql (direct connection):"
echo '   psql $(kubectl -n postgres get secrets postgres-cluster-pguser-conversas-ai-user -o go-template="{{.data.uri | base64decode}}")'
echo ""
echo "🔌 Connect via port-forward:"
echo "   # In one terminal:"
echo '   PG_CLUSTER_PRIMARY_POD=$(kubectl get pod -n postgres -o name -l postgres-operator.crunchydata.com/cluster=postgres-cluster,postgres-operator.crunchydata.com/role=master)'
echo '   kubectl -n postgres port-forward "${PG_CLUSTER_PRIMARY_POD}" 5432:5432'
echo ""
echo "   # In another terminal:"
echo '   PG_CLUSTER_USER_SECRET_NAME=postgres-cluster-pguser-conversas-ai-user'
echo '   PGPASSWORD=$(kubectl get secrets -n postgres "${PG_CLUSTER_USER_SECRET_NAME}" -o go-template="{{.data.password | base64decode}}") \'
echo '   PGUSER=$(kubectl get secrets -n postgres "${PG_CLUSTER_USER_SECRET_NAME}" -o go-template="{{.data.user | base64decode}}") \'
echo '   PGDATABASE=$(kubectl get secrets -n postgres "${PG_CLUSTER_USER_SECRET_NAME}" -o go-template="{{.data.dbname | base64decode}}") \'
echo '   psql -h localhost'
echo ""
echo "💡 Remember: After connecting, you may want to create a user schema:"
echo "   CREATE SCHEMA \"conversas-ai-user\" AUTHORIZATION \"conversas-ai-user\";"
echo ""
echo "🔧 Useful commands:"
echo "   # View cluster status:"
echo "   kubectl -n postgres get postgrescluster"
echo ""
echo "   # View all resources:"
echo "   kubectl -n postgres get all"
echo ""
echo "   # View cluster events:"
echo "   kubectl -n postgres get events --sort-by='.firstTimestamp'"
echo ""
echo "   # Delete cluster (if needed):"
echo "   kubectl -n postgres delete postgrescluster postgres-cluster"
echo ""
echo "🚨 PRODUCTION NOTES:"
echo "   - This cluster has 2 replicas for high availability"
echo "   - Backup retention is set to 30 days"
echo "   - Resources are optimized for production workloads"
echo "   - Make sure to update passwords in pg-migrate-secrets.yaml"
echo "   - Monitor the cluster regularly using kubectl commands above"
