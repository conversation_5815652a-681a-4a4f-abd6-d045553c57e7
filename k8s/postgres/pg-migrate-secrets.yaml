apiVersion: v1
kind: Secret
metadata:
  name: pg-migrate-secrets
  namespace: postgres
  labels:
    app: pg-migrate
    environment: production
type: Opaque
stringData:
  POSTGRES_HOST: "postgres-cluster-primary.postgres.svc"
  POSTGRES_PORT: "5432"
  POSTGRES_DB: "conversas_ai"
  POSTGRES_USER: "conversas-ai-user"
  POSTGRES_PASSWORD: "CHANGE_ME_IN_PRODUCTION"  # Deve ser alterado para produção
  DB_SCHEMA: "public"
  # MIGRATE_LIMIT: "100" # sem limit para copiar tudo
  MIGRATION_DO_TRUNCATE: "false"  # Nunca truncar em produção
  GCP_BIGQUERY_DATASET: "production"
  GOOGLE_APPLICATION_CREDENTIALS: "/app/src/connections/conversas-ai.json"
---
apiVersion: v1
data:
  conversas-ai.json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
kind: Secret
metadata:
  name: conversas-ai-service-account
  namespace: postgres
  labels:
    environment: production
type: Opaque
