#!/bin/bash

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funções de log
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar se kubectl está disponível
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl não encontrado. Por favor, instale kubectl primeiro."
        exit 1
    fi
    log_success "kubectl encontrado"
}

# Verificar se o namespace postgres existe
check_namespace() {
    log_info "Verificando se o namespace postgres existe..."
    if ! kubectl get namespace postgres &> /dev/null; then
        log_error "Namespace postgres não encontrado. Execute primeiro o deploy-postgres.sh"
        exit 1
    fi
    log_success "Namespace postgres encontrado"
}

# Verificar se o cluster PostgreSQL está rodando
check_postgres_cluster() {
    log_info "Verificando se o cluster PostgreSQL está rodando..."
    
    if ! kubectl get postgrescluster postgres-cluster -n postgres &> /dev/null; then
        log_error "Cluster postgres-cluster não encontrado"
        exit 1
    fi
    
    # Verificar se o cluster está ready
    local cluster_status=$(kubectl get postgrescluster postgres-cluster -n postgres -o jsonpath='{.status.conditions[?(@.type=="PGBackRestReplicaRepoReady")].status}' 2>/dev/null || echo "Unknown")
    
    if [ "$cluster_status" != "True" ]; then
        log_warning "Cluster PostgreSQL não está completamente pronto. Status: $cluster_status"
        log_info "Aguardando cluster ficar pronto..."
        kubectl -n postgres wait --for=condition=PGBackRestReplicaRepoReady postgrescluster/postgres-cluster --timeout=600s
    fi
    
    log_success "Cluster PostgreSQL está rodando e pronto"
}

# Criar ConfigMap com scripts SQL
create_configmap() {
    log_info "Criando ConfigMap com scripts SQL..."
    
    # Verificar se o arquivo create_tables.sql existe
    if [ ! -f "create_tables.sql" ]; then
        log_error "Arquivo create_tables.sql não encontrado no diretório atual"
        exit 1
    fi
    
    # Remover ConfigMap existente se houver
    if kubectl get configmap sql-scripts-config -n postgres &> /dev/null; then
        log_warning "ConfigMap sql-scripts-config já existe. Removendo..."
        kubectl delete configmap sql-scripts-config -n postgres
    fi
    
    # Criar novo ConfigMap
    kubectl create configmap sql-scripts-config --from-file=create_tables.sql --namespace=postgres
    log_success "ConfigMap sql-scripts-config criado com sucesso"
}

# Executar job de criação de tabelas
run_create_tables_job() {
    log_info "Executando job de criação de tabelas..."
    
    # Remover job existente se houver
    if kubectl get job create-tables-job -n postgres &> /dev/null; then
        log_warning "Job create-tables-job já existe. Removendo..."
        kubectl delete job create-tables-job -n postgres
        # Aguardar um pouco para o job ser removido completamente
        sleep 5
    fi
    
    # Aplicar o job
    if [ ! -f "create-tables-job.yaml" ]; then
        log_error "Arquivo create-tables-job.yaml não encontrado"
        exit 1
    fi
    
    kubectl apply -f create-tables-job.yaml
    log_success "Job create-tables-job criado com sucesso"
    
    # Aguardar o job completar
    log_info "Aguardando conclusão do job (timeout: 600 segundos)..."
    
    # Monitorar o job
    local timeout=600
    local elapsed=0
    local interval=10
    
    while [ $elapsed -lt $timeout ]; do
        local job_status=$(kubectl get job create-tables-job -n postgres -o jsonpath='{.status.conditions[0].type}' 2>/dev/null || echo "")
        
        if [ "$job_status" = "Complete" ]; then
            log_success "Job create-tables-job concluído com sucesso!"
            break
        elif [ "$job_status" = "Failed" ]; then
            log_error "Job create-tables-job falhou!"
            log_info "Logs do job:"
            kubectl logs -n postgres -l app=create-tables --tail=50
            exit 1
        fi
        
        echo -n "."
        sleep $interval
        elapsed=$((elapsed + interval))
    done
    
    if [ $elapsed -ge $timeout ]; then
        log_error "Timeout aguardando conclusão do job"
        log_info "Logs do job:"
        kubectl logs -n postgres -l app=create-tables --tail=50
        exit 1
    fi
}

# Mostrar logs do job
show_job_logs() {
    log_info "Logs da execução do job:"
    echo "----------------------------------------"
    kubectl logs -n postgres -l app=create-tables
    echo "----------------------------------------"
}

# Verificar tabelas criadas
verify_tables() {
    log_info "Verificando tabelas criadas no banco..."
    
    # Obter pod do PostgreSQL
    local pg_pod=$(kubectl get pods -n postgres -l postgres-operator.crunchydata.com/role=master -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    
    if [ -z "$pg_pod" ]; then
        log_error "Pod master do PostgreSQL não encontrado"
        return 1
    fi
    
    # Listar tabelas
    log_info "Tabelas existentes no banco:"
    kubectl exec -n postgres "$pg_pod" -c database -- psql -U conversas-ai-user -d conversas_ai -c "\dt" || {
        log_warning "Não foi possível listar as tabelas diretamente"
        log_info "Você pode verificar manualmente conectando ao banco"
    }
}

# Executar job de migração de dados (se existir)
run_migration_job() {
    if [ -f "pg-migrate-job.yaml" ]; then
        log_info "Encontrado arquivo pg-migrate-job.yaml. Executando migração de dados..."
        
        # Remover job existente se houver
        if kubectl get job pg-migrate-job -n postgres &> /dev/null; then
            log_warning "Job pg-migrate-job já existe. Removendo..."
            kubectl delete job pg-migrate-job -n postgres
            sleep 5
        fi
        
        kubectl apply -f pg-migrate-job.yaml
        log_info "Job de migração de dados iniciado. Monitore com:"
        echo "kubectl logs -n postgres -l app=pg-migrate -f"
    else
        log_info "Arquivo pg-migrate-job.yaml não encontrado. Pulando migração de dados."
    fi
}

# Cleanup opcional
cleanup_jobs() {
    if [ "$1" = "--cleanup" ]; then
        log_info "Limpando jobs executados..."
        kubectl delete job create-tables-job -n postgres 2>/dev/null || true
        kubectl delete job pg-migrate-job -n postgres 2>/dev/null || true
        log_success "Cleanup concluído"
    fi
}

# Mostrar informações de conexão
show_connection_info() {
    log_info "Informações de conexão PostgreSQL PRODUÇÃO:"
    echo "=========================================="
    echo "Cluster: postgres-cluster"
    echo "Namespace: postgres"
    echo "Database: conversas_ai"
    echo "Username: conversas-ai-user"
    echo ""
    echo "🔑 Para obter a senha:"
    echo 'kubectl -n postgres get secret postgres-cluster-pguser-conversas-ai-user -o go-template="{{.data.password | base64decode}}"'
    echo ""
    echo "🔌 Para conectar via psql:"
    echo 'psql $(kubectl -n postgres get secrets postgres-cluster-pguser-conversas-ai-user -o go-template="{{.data.uri | base64decode}}")'
    echo ""
    echo "⚠️  IMPORTANTE: Este é o ambiente de PRODUÇÃO!"
    echo "   - Faça backup antes de qualquer alteração"
    echo "   - Monitore o desempenho após mudanças"
    echo "   - Use as credenciais com cuidado"
    echo "=========================================="
}

# Função principal
main() {
    echo "🚀 Iniciando migração PostgreSQL PRODUÇÃO..."
    echo ""
    
    # Confirmação de segurança para produção
    log_warning "ATENÇÃO: Este script irá executar migrações no ambiente de PRODUÇÃO!"
    read -p "Tem certeza que deseja continuar? (digite 'PRODUÇÃO' para confirmar): " confirmation
    if [ "$confirmation" != "PRODUÇÃO" ]; then
        log_error "Migração cancelada."
        exit 1
    fi
    
    # Verificações iniciais
    check_kubectl
    check_namespace
    check_postgres_cluster
    
    echo ""
    
    # Criar ConfigMap
    create_configmap
    
    echo ""
    
    # Executar job de criação de tabelas
    run_create_tables_job
    
    echo ""
    
    # Mostrar logs
    show_job_logs
    
    echo ""
    
    # Verificar tabelas
    verify_tables
    
    echo ""
    
    # Executar migração de dados se disponível
    run_migration_job
    
    echo ""
    
    # Cleanup se solicitado
    cleanup_jobs "$1"
    
    echo ""
    
    # Mostrar informações finais
    show_connection_info
    
    echo ""
    log_success "Migração PostgreSQL PRODUÇÃO concluída com sucesso! 🎉"
}

# Verificar argumentos
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Script de Migração PostgreSQL PRODUÇÃO"
    echo ""
    echo "Uso: $0 [--cleanup]"
    echo ""
    echo "Opções:"
    echo "  --cleanup    Remove os jobs após execução"
    echo "  --help, -h   Mostra esta mensagem de ajuda"
    echo ""
    echo "O script executa as seguintes operações:"
    echo "1. Verifica se o cluster PostgreSQL está rodando"
    echo "2. Cria ConfigMap com scripts SQL"
    echo "3. Executa job de criação de tabelas"
    echo "4. Verifica se as tabelas foram criadas"
    echo "5. Executa job de migração de dados (se disponível)"
    echo ""
    echo "⚠️  IMPORTANTE: Este script é para o ambiente de PRODUÇÃO!"
    echo ""
    exit 0
fi

# Executar função principal
main "$1"
