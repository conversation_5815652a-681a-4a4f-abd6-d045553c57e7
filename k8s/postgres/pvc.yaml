apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-data-pvc
  namespace: postgres
  labels:
    app: postgres
    component: data
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 200Gi  # Maior storage para dados de produção (200Gi para 2 réplicas) (100Gi para 1 réplica)
  storageClassName: standard-rwo
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-backup-pvc
  namespace: postgres
  labels:
    app: postgres
    component: backup
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi  # Maior storage para backups de produção
  storageClassName: standard-rwo
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-wal-pvc
  namespace: postgres
  labels:
    app: postgres
    component: wal
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi  # Maior storage para WAL de produção
  storageClassName: standard-rwo
