apiVersion: postgres-operator.crunchydata.com/v1beta1
kind: PostgresCluster
metadata:
  name: postgres-cluster
  namespace: postgres
  labels:
    app: postgres
    environment: production
spec:
  # Usar a imagem padrão mais recente - o operator gerencia as versões
  postgresVersion: 15

  # Instance configuration - recursos otimizados para produção
  instances:
    - name: instance1
      replicas: 1  # Alta disponibilidade para produção
      resources:
        requests:
          cpu: "1000m"
          memory: "2Gi"
        limits:
          cpu: "4000m"
          memory: "8Gi"
      dataVolumeClaimSpec:
        accessModes:
          - "ReadWriteOnce"
        resources:
          requests:
            storage: 200Gi  # Maior storage para dados de produção (200Gi para 2 réplicas) (100Gi para 1 réplica)
        # Usar storageClass padrão se standard-rwo não existir
        storageClassName: standard
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              podAffinityTerm:
                topologyKey: kubernetes.io/hostname
                labelSelector:
                  matchLabels:
                    postgres-operator.crunchydata.com/cluster: postgres-cluster
                    postgres-operator.crunchydata.com/instance-set: instance1

  # Backup configuration usando volume local para produção
  backups:
    pgbackrest:
      repos:
        - name: repo1
          volume:
            volumeClaimSpec:
              accessModes:
                - "ReadWriteOnce"
              resources:
                requests:
                  storage: 100Gi  # Maior storage para backups de produção
              storageClassName: standard
      # Configuração de retenção para ambiente de produção
      global:
        repo1-retention-full: "30"  # 30 dias de retenção para produção
        repo1-retention-full-type: "time"
        repo1-retention-diff: "14"  # 14 dias de backups diferenciais
        repo1-retention-diff-type: "time"

  # User and database configuration
  users:
    - name: conversas-ai-user
      databases:
        - conversas_ai
      options: "CREATEDB CREATEROLE"
      password:
        type: AlphaNumeric

  # PostgreSQL configuration otimizada para produção
  patroni:
    dynamicConfiguration:
      postgresql:
        parameters:
          log_connections: "on"
          log_disconnections: "on"

          # Configurações de conexão para produção
          max_connections: "500"

          # Configurações de memória (otimizada para 8Gi limit)
          shared_buffers: "1GB"           # ~12.5% da RAM
          effective_cache_size: "6GB"     # ~75% da RAM
          work_mem: "8MB"                 # Para operações de ordenação
          maintenance_work_mem: "256MB"   # Para operações de manutenção

          # Configurações de WAL para produção
          wal_buffers: "32MB"
          min_wal_size: "1GB"
          max_wal_size: "8GB"
          checkpoint_completion_target: "0.9"

          # Configurações de performance para produção
          random_page_cost: "1.1"              # Para SSD
          effective_io_concurrency: "200"      # Para SSD
          default_statistics_target: "100"

          # Configurações de workers (otimizada para recursos de produção)
          max_worker_processes: "8"
          max_parallel_workers: "6"
          max_parallel_workers_per_gather: "3"
          max_parallel_maintenance_workers: "3"

          # Configurações específicas para ambiente de produção
          log_statement: "ddl"           # Log apenas DDL em produção
          log_min_duration_statement: "5000"  # Log queries > 5s
          log_line_prefix: "[%t] %u@%d "

          # Timezone
          timezone: "America/Sao_Paulo"

          # Configurações de autovacuum para produção
          autovacuum: "on"
          autovacuum_max_workers: "4"
          autovacuum_naptime: "15s"

  # Monitoring - Recursos adequados para produção
  monitoring:
    pgmonitor:
      exporter:
        resources:
          requests:
            cpu: "100m"
            memory: "128Mi"
          limits:
            cpu: "200m"
            memory: "256Mi"
