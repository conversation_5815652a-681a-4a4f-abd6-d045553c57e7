grafana:
  persistence:
    enabled: true
    type: pvc
    size: 10Gi
  ingress:
    enabled: true
    ingressClassName: nginx
    hosts:
      - grafana.conversas.ai
  adminPassword: 'Oogf@r7&E715'

  # Configuração de datasources
  additionalDataSources:
    - name: Loki
      type: loki
      url: http://loki.monitoring.svc.cluster.local:3100
      access: proxy
      isDefault: false
      jsonData:
        maxLines: 1000
        derivedFields:
          - datasourceUid: prometheus
            matcherRegex: "traceID=(\\w+)"
            name: TraceID
            url: "$${__value.raw}"
      editable: true
  
prometheus:
  prometheusSpec:
    retention: 30d
    retentionSize: 45GiB
    storageSpec:
      volumeClaimTemplate:
        spec:
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 50Gi
alertmanager:
  alertmanagerSpec:
    storage:
      volumeClaimTemplate:
        spec:
          storageClassName: "gp2"
          accessModes:
            - ReadWriteOnce
          resources:
            requests:
              storage: 5Gi