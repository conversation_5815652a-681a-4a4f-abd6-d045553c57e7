{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 30, "links": [], "panels": [{"datasource": {"type": "loki", "uid": "P8E80F9AEF21F6940"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "loki", "uid": "P8E80F9AEF21F6940"}, "direction": "backward", "editorMode": "code", "expr": "max_over_time({namespace=\"conversas-ai\"} |= `FUNC_TIME` | regexp `\\[FUNC_TIME\\]-(?P<function_name>\\w+)-(?P<execution_time>\\d+)ms` | line_format `{{.function_name}} {{.execution_time}}` | unwrap execution_time [$__auto]) by (function_name)", "queryType": "range", "refId": "A"}], "title": "Function Execution Max Time", "type": "timeseries"}, {"datasource": {"type": "loki", "uid": "P8E80F9AEF21F6940"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "loki", "uid": "P8E80F9AEF21F6940"}, "expr": "sum by (function_name) (count_over_time({app=~\"conversas-ai-api|conversas-ai-worker\"} |= \"FUNC_TIME\" | regexp `\\\\[FUNC_TIME\\\\]-(?P<function_name>\\\\w+)-(?P<execution_time>\\\\d+)ms` [5m]))", "legendFormat": "{{function_name}}", "refId": "A"}], "title": "Function Execution Count (Last 5 minutes)", "type": "timeseries"}, {"datasource": {"type": "loki", "uid": "P8E80F9AEF21F6940"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "id": 5, "options": {"dedupStrategy": "none", "enableInfiniteScrolling": false, "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": true, "showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false}, "pluginVersion": "11.6.0", "targets": [{"datasource": {"type": "loki", "uid": "P8E80F9AEF21F6940"}, "expr": "{app=~\"conversas-ai-api|conversas-ai-worker\"} |= \"FUNC_TIME\" | regexp `\\\\[FUNC_TIME\\\\]-(?P<function_name>\\\\w+)-(?P<execution_time>\\\\d+)ms`", "refId": "A"}], "title": "Recent Function Executions", "type": "logs"}], "preload": false, "refresh": "30s", "schemaVersion": 41, "tags": ["conversas-ai", "performance", "functions"], "templating": {"list": []}, "time": {"from": "now-12h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Performance Monitoring - Conversas AI Functions", "uid": "aeoo8h2u0ne9sf", "version": 8}