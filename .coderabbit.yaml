# Configuração CodeRabbit otimizada para projetos Angular - Foco em Potential Issues
language: "pt-BR"
tone_instructions: "Seja direto e foque EXCLUSIVAMENTE em potential issues: bugs, vulnerabilidades de segurança, memory leaks, problemas de lifecycle, race conditions e problemas de performance. Ignore completamente estilo, formatação e sugestões de refatoração."

reviews:
  # Perfil assertivo para detectar mais potential issues
  profile: "chill"

  # Configurações otimizadas para focar apenas em potential issues críticos
  high_level_summary: false
  changed_files_summary: false
  sequence_diagrams: false
  poem: true
  suggested_labels: false
  estimate_code_review_effort: true
  assess_linked_issues: false
  related_issues: true
  related_prs: true
  suggested_reviewers: true
  collapse_walkthrough: true

  # Desabilitar funcionalidades que não são potential issues
  finishing_touches:
    docstrings:
      enabled: false
    unit_tests:
      enabled: false

  # Configurações de auto review para branches principais
  auto_review:
    enabled: true
    auto_incremental_review: true
    drafts: false
    base_branches:
      - master
      - main
      - development
      - develop

  # Filtros de caminho para ignorar arquivos gerados e desnecessários
  path_filters:
    # Arquivos gerados automaticamente
    - "!**/*.ngfactory.ts"
    - "!**/*.generated.ts"
    - "!**/*.min.js"
    - "!**/*.min.css"
    - "!**/*.d.ts"

    # Diretórios de build e dependências
    - "!dist/**"
    - "!build/**"
    - "!node_modules/**"
    - "!coverage/**"
    - "!.angular/**"

    # Arquivos de configuração que não precisam de revisão
    - "!.coderabbit.yaml"
    - "!**/*.map"

  # Instruções específicas para Angular/TypeScript focadas em potential issues
  path_instructions:
    - path: "**/*.{ts,js}"
      instructions: |
        FOQUE EXCLUSIVAMENTE EM POTENTIAL ISSUES CRÍTICOS:

        🐛 BUGS E ERROS ANGULAR:
        - Memory leaks em subscriptions não unsubscribed (OnDestroy)
        - Uso incorreto de async pipe que pode causar multiple subscriptions
        - Change detection issues que podem causar ExpressionChangedAfterItHasBeenCheckedError
        - Null/undefined access em templates que podem causar runtime errors
        - Incorrect lifecycle hook usage (ngOnInit vs constructor)
        - Route guards que podem bloquear navegação incorretamente
        - Circular dependencies entre modules/components
        - Incorrect use of ViewChild/ContentChild que pode retornar undefined

        🔒 SEGURANÇA:
        - XSS vulnerabilities em innerHTML ou bypassSecurityTrust
        - CSRF issues em HTTP requests sem proper headers
        - Insecure HTTP requests (http vs https)
        - Exposure of sensitive data em console.log statements
        - Improper sanitization de user inputs

        ⚡ PERFORMANCE E MEMÓRIA:
        - Memory leaks em event listeners não removidos
        - Excessive change detection triggers
        - Large bundle sizes devido a imports desnecessários
        - Inefficient *ngFor sem trackBy
        - Subscription leaks em services
        - Improper use of OnPush change detection

        🧵 ASYNC E OBSERVABLES:
        - Race conditions em async operations
        - Unhandled promise rejections
        - Incorrect error handling em observables
        - Multiple subscriptions ao mesmo observable
        - Incorrect use of switchMap vs mergeMap vs concatMap

        ❌ NÃO COMENTE SOBRE:
        - Estilo de código, formatação, naming conventions
        - Refatorações que não corrigem bugs
        - Otimizações prematuras
        - Padrões de design que funcionam corretamente
        - Atualizações de bibliotecas a menos que sejam correções de bugs ou segurança
        - Arquivos gerados automaticamente
        - Sugestões de refatoração em classes, métodos ou estruturas a menos que resolvam problemas de funcionalidade ou performance significativas

    - path: "**/*.component.ts"
      instructions: |
        FOQUE EM POTENTIAL ISSUES DE COMPONENTES:
        - Lifecycle hooks incorretos que podem causar bugs
        - Memory leaks em subscriptions
        - Incorrect use de ViewChild que pode ser undefined
        - Change detection problems

    - path: "**/*.service.ts"
      instructions: |
        FOQUE EM POTENTIAL ISSUES DE SERVICES:
        - Singleton services com state mutável compartilhado
        - Memory leaks em subscriptions de services
        - Incorrect error handling em HTTP requests
        - Race conditions em async operations

    - path: "**/package.json"
      instructions: |
        FOQUE EM POTENTIAL ISSUES DE DEPENDÊNCIAS:
        - Vulnerabilidades conhecidas em versões de dependências
        - Conflitos de versões que podem causar runtime errors
        - Dependências peer que podem estar faltando
        - Versões incompatíveis do Angular com outras libs

    - path: "**/*.html"
      instructions: |
        FOQUE EM POTENTIAL ISSUES DE TEMPLATES:
        - Null/undefined access que pode causar runtime errors
        - Incorrect event binding que pode causar memory leaks
        - Missing trackBy em *ngFor que pode causar performance issues
        - Incorrect use de async pipe

  # Ferramentas de análise estática habilitadas (baseadas no schema oficial)
  tools:
    # Análise de JavaScript/TypeScript
    eslint:
      enabled: true
    biome:
      enabled: true
    oxc:
      enabled: true
    # Análise de segurança
    semgrep:
      enabled: true
    gitleaks:
      enabled: true
    # Análise de HTML
    htmlhint:
      enabled: true
    # Desabilitar ferramentas não relacionadas a Angular
    ruff:
      enabled: false
    pylint:
      enabled: false

# Configurações de base de conhecimento otimizadas para Angular
knowledge_base:
  # Desabilitar para focar apenas em potential issues detectados pelo CodeRabbit
  opt_out: false
  web_search:
    enabled: true
  code_guidelines:
    enabled: true
    filePatterns:
      - "**/.eslintrc*"
      - "**/tsconfig.json"
      - "**/angular.json"
      - "**/tslint.json"
      - "**/README.md"
      - "**/SECURITY.md"
  learnings:
    scope: "local"
  issues:
    scope: "local"
